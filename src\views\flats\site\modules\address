      
      <!-- 页面载入后，调用init函数 -->
      <div id="container" v-bind:class="{ active: isActive, noshow:isShow}"></div>
      
      
      <a-form-model-item label="站点地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address" @>
         <a-input v-model="model.address" placeholder="请输入地址" disabled  style="width: 200px;"></a-input>
         <a-button type="primary" icon="arrow-down"  v-on:click="admap()" />
      </a-form-model-item>
      
      
      let script = document.createElement("script");
      //设置标签的type属性
      script.type = "text/javascript";
      //设置标签的链接地址
      script.src = "https://map.qq.com/api/gljs?v=1.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77";
      //添加标签到dom
      document.body.appendChild(script);
      
      
      isShow:false,
      isActive:false,
      isDeduplication_map:false,
      
      admap(){
        var that = this;
        if(!that.isActive) {
          //样式显示
          that.isShow = false;
          that.isActive = true;
          if(!that.isDeduplication_map) {
            //定义地图中心点坐标
            var center = new TMap.LatLng(39.984120, 116.307484)
            //定义map变量，调用 TMap.Map() 构造函数创建地图
            var map = new TMap.Map(document.getElementById('container'), {
                center: center,//设置地图中心点坐标
            }); 
            //坐标设置
            //初始化marker图层
            var markerLayer = new TMap.MultiMarker({
                id: 'marker-layer',
                map: map
            });
            //定义坐标拾取器
            var clickHandler=function(evt){
                var lat = evt.latLng.getLat().toFixed(7);
                var lng = evt.latLng.getLng().toFixed(7);
                that.model.address = lat + "," + lng;
                that.model.lat = lat;
                that.model.lng = lng;
                markerLayer.setGeometries([])
                markerLayer.add({
                  position: evt.latLng
                });   
                console.log("您点击的的坐标是："+ lat + "," + lng);
            }
            //Map实例创建后，通过on方法绑定点击事件
            map.on("click",clickHandler)
            that.isDeduplication_map = true;
          }
        } else {
          that.isShow = true;
          that.isActive = false;
        }
      },