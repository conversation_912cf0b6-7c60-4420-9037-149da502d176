<template>
  <a-tabs v-model:activeKey="activeKey" @change="changeTab()">
    <a-tab-pane key="1" tab="求租需求表单配置">
      <a-card :bordered="false">
        <template v-for="(val, index) in list">
          <a-form-model-item :label="val.name">
            <draggable v-model="val.value" :group="val.name" @start="drag=true" @end="drag=false">
              <a-tag v-for="(tag, index2) in val.value" :key="tag" :color="val.color" closable @close="() => handleClose(tag,index)">
                {{ tag }}
              </a-tag>
            </draggable>
            <a-input
            v-if="val.inputVisible"
            ref="input"
            type="text"
            size="small"
            :style="{ width: '78px' }"
            :value="inputValue"
            @change="handleInputChange"
            @blur="handleInputConfirm(index)"
            @keyup.enter="handleInputConfirm(index)"
          />
          <a-tag v-else style="background: #fff; borderStyle: dashed;" @click="showInput(index)">
            <a-icon type="plus" /> 增加新选项
          </a-tag>
          </a-form-model-item>
        </template>
          <a-button type="primary" @click="submitForm" block :loading="loading">提交</a-button>
      </a-card>
    </a-tab-pane>
    <a-tab-pane key="2" tab="出租需求表单配置">
      <a-card :bordered="false">
        <template v-for="(val, index) in list">
          <a-form-model-item :label="val.name">
            <draggable v-model="val.value" :group="val.name" @start="drag=true" @end="drag=false">
              <a-tag v-for="(tag, index2) in val.value" :key="tag" :color="val.color" closable @close="() => handleClose(tag,index)">
                {{ tag }}
              </a-tag>
            </draggable>
            <a-input
            v-if="val.inputVisible"
            ref="input"
            type="text"
            size="small"
            :style="{ width: '78px' }"
            :value="inputValue"
            @change="handleInputChange"
            @blur="handleInputConfirm(index)"
            @keyup.enter="handleInputConfirm(index)"
          />
          <a-tag v-else style="background: #fff; borderStyle: dashed;" @click="showInput(index)">
            <a-icon type="plus" /> 增加新选项
          </a-tag>
          </a-form-model-item>
        </template>
          <a-button type="primary" @click="submitForm" block :loading="loading">提交</a-button>
      </a-card>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import JEditor from '@/components/jeecg/JEditor'
import { httpAction } from '@/api/manage'
import draggable from 'vuedraggable'
export default {
  components: {
    JEditor,
    draggable
  },
  props: [],
  data() {
    return {
      loading:false,
      activeKey:"1",
      inputValue:'', // 用于存放新增值
      list:[

      ]
    }
  },
  computed: {},
  watch: {},
  created() {
    // https://api.xiouxie.com
    httpAction('https://api.rent.zkshlm.com/common/Form', {activeKey:this.activeKey}, 'post')
      .then(res => {
          this.list = res.data
      })
      .finally(() => {})
  },
  mounted() {

  },
  methods: {
    submitForm() {
      let that = this
      that.loading = true
      httpAction('https://api.rent.zkshlm.com/common/Form/updateForm', {data:this.list,activeKey:this.activeKey}, 'post')
        .then(res => {
          if (res.code == '200') {
            that.$message.success(res.data)
          } else {
            that.$message.warning(res.msg)
          }
          that.loading = false
        })
        .finally(() => {
          that.loading = false
        })
    },
    handleInputChange(e) {
      this.inputValue = e.target.value
    },
    handleInputConfirm(index) {
      // 赋值
      let inputValue = this.inputValue
      let value2 = this.list[index].value
      if(!value2) {
        value2 = [inputValue]
      } else {
        if (inputValue && value2.indexOf(inputValue) === -1) {
          value2 = [...value2, inputValue]
        } else {
          this.$message.warning(inputValue+'值已存在')
        }
      }
      this.list[index].value = value2
      this.inputValue = ''
      this.list[index].inputVisible = false
    },
    showInput(index) {
      this.list[index].inputVisible = true
    },
    handleClose(val, index) {
      const value = this.list[index].value.filter(value => value !== val)
      this.list[index].value = value
    },
    changeTab() {
      httpAction('https://api.rent.zkshlm.com/common/Form', {activeKey:this.activeKey}, 'post')
      .then(res => {
          this.list = res.data
      })
      .finally(() => {})
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
