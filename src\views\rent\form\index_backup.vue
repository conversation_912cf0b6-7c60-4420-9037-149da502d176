<template>
  <a-tabs v-model:activeKey="activeKey">
    <a-tab-pane key="1" tab="求租需求表单配置">
      <a-card :bordered="false">
        <a-form-model
          ref="elForm"
          :model="formData"
          :rules="rules"
          :label-col="{ span: 3, offset: 0 }"
          :wrapper-col="{ span: 21, offset: 0 }"
          layout="horizontal"
        >

        <template v-for="(val, index) in list">
          <a-form-model-item :label="val.name">
            <draggable v-model="val.value" :group="val.name" @start="drag=true" @end="drag=false">
              <a-tag v-for="(tag, index2) in val.value" :key="index2" :color="val.color" closable @close="() => handleClose(tag,index)">
                {{ tag }}
              </a-tag>
            </draggable>
            <a-input
            v-if="val.inputVisible"
            ref="input"
            type="text"
            size="small"
            :style="{ width: '78px' }"
            :value="val.inputValue"
            @change="handleInputChange(val)"
            @blur="handleInputConfirm(val)"
            @keyup.enter="handleInputConfirm(val)"
          />
          <a-tag v-else style="background: #fff; borderStyle: dashed;" @click="showInput(val)">
            <a-icon type="plus" /> 增加新选项
          </a-tag>
          </a-form-model-item>
        </template>




        <a-form-model-item label="户型">
          <draggable v-model="tags_v8" group="tags_v8" @start="drag=true" @end="drag=false">
            <template v-for="(tag, index) in tags_v8">
              <a-tag :key="tag" color="#f50" closable @close="() => handleClose(tag,'v8')">
                {{ tag }}
              </a-tag>
            </template>
          </draggable>
          <a-input
            v-if="inputVisible_v1"
            ref="input"
            type="text"
            size="small"
            :style="{ width: '78px' }"
            :value="inputValue_v1"
            @change="handleInputChange_v1"
            @blur="handleInputConfirm('v8')"
            @keyup.enter="handleInputConfirm('v8')"
          />
          <a-tag v-else style="background: #fff; borderStyle: dashed;" @click="showInput('inputVisible_v8')">
            <a-icon type="plus" /> 增加新选项
          </a-tag>
        </a-form-model-item>

          <a-form-model-item label="朝向">
            <draggable v-model="tags_v1" group="tags_v1" @start="drag=true" @end="drag=false">
              <template v-for="(tag, index) in tags_v1">
                <a-tag :key="tag" color="#f50" closable @close="() => handleClose(tag,'v1')">
                  {{ tag }}
                </a-tag>
              </template>
            </draggable>
            <a-input
              v-if="inputVisible_v1"
              ref="input"
              type="text"
              size="small"
              :style="{ width: '78px' }"
              :value="inputValue_v1"
              @change="handleInputChange_v1"
              @blur="handleInputConfirm('v1')"
              @keyup.enter="handleInputConfirm('v1')"
            />
            <a-tag v-else style="background: #fff; borderStyle: dashed;" @click="showInput('inputVisible_v1')">
              <a-icon type="plus" /> 增加新选项
            </a-tag>
          </a-form-model-item>
          <a-form-model-item label="面积">
            <draggable v-model="tags_v2" group="tags_v2" @start="drag=true" @end="drag=false">
              <template v-for="(tag, index) in tags_v2">
                <a-tag :key="tag" color="#2db7f5" closable @close="() => handleClose(tag,'v2')">
                  {{ tag }}
                </a-tag>
              </template>
            </draggable>
            <a-input
              v-if="inputVisible_v2"
              ref="input"
              type="text"
              size="small"
              :style="{ width: '78px' }"
              :value="inputValue_v2"
              @change="handleInputChange_v2"
              @blur="handleInputConfirm('v2')"
              @keyup.enter="handleInputConfirm('v2')"
            />
            <a-tag v-else style="background: #fff; borderStyle: dashed;" @click="showInput('inputVisible_v2')">
              <a-icon type="plus" /> 增加新选项
            </a-tag>
          </a-form-model-item>
          <a-form-model-item label="楼层">
            <draggable v-model="tags_v3" group="tags_v3" @start="drag=true" @end="drag=false">
            <template v-for="(tag, index) in tags_v3">
              <a-tag :key="tag" color="#87d068" closable @close="() => handleClose(tag,'v3')">
                {{ tag }}
              </a-tag>
            </template>
            </draggable>
            <a-input
              v-if="inputVisible_v3"
              ref="input"
              type="text"
              size="small"
              :style="{ width: '78px' }"
              :value="inputValue_v3"
              @change="handleInputChange_v3"
              @blur="handleInputConfirm('v3')"
              @keyup.enter="handleInputConfirm('v3')"
            />
            <a-tag v-else style="background: #fff; borderStyle: dashed;" @click="showInput('inputVisible_v3')">
              <a-icon type="plus" /> 增加新选项
            </a-tag>
          </a-form-model-item>
          <a-form-model-item label="租期">
            <draggable v-model="tags_v4" group="tags_v4" @start="drag=true" @end="drag=false">
            <template v-for="(tag, index) in tags_v4">
              <a-tag :key="tag" color="#108ee9" closable @close="() => handleClose(tag,'v4')">
                {{ tag }}
              </a-tag>
            </template>
          </draggable>
            <a-input
              v-if="inputVisible_v4"
              ref="input"
              type="text"
              size="small"
              :style="{ width: '78px' }"
              :value="inputValue_v4"
              @change="handleInputChange_v4"
              @blur="handleInputConfirm('v4')"
              @keyup.enter="handleInputConfirm('v4')"
            />
            <a-tag v-else style="background: #fff; borderStyle: dashed;" @click="showInput('inputVisible_v4')">
              <a-icon type="plus" /> 增加新选项
            </a-tag>
          </a-form-model-item>
          <a-form-model-item label="电梯">
            <draggable v-model="tags_v5" group="tags_v5" @start="drag=true" @end="drag=false">
            <template v-for="(tag, index) in tags_v5">
              <a-tag :key="tag" color="red" closable @close="() => handleClose(tag,'v5')">
                {{ tag }}
              </a-tag>
            </template>
            </draggable>
            <a-input
              v-if="inputVisible_v5"
              ref="input"
              type="text"
              size="small"
              :style="{ width: '78px' }"
              :value="inputValue_v5"
              @change="handleInputChange_v5"
              @blur="handleInputConfirm('v5')"
              @keyup.enter="handleInputConfirm('v5')"
            />
            <a-tag v-else style="background: #fff; borderStyle: dashed;" @click="showInput('inputVisible_v5')">
              <a-icon type="plus" /> 增加新选项
            </a-tag>
          </a-form-model-item>
          <a-form-model-item label="供暖">
            <draggable v-model="tags_v6" group="tags_v6" @start="drag=true" @end="drag=false">
            <template v-for="(tag, index) in tags_v6">
              <a-tag :key="tag" color="cyan" closable @close="() => handleClose(tag,'v6')">
                {{ tag }}
              </a-tag>
            </template>
            </draggable>
            <a-input
              v-if="inputVisible_v6"
              ref="input"
              type="text"
              size="small"
              :style="{ width: '78px' }"
              :value="inputValue_v6"
              @change="handleInputChange_v6"
              @blur="handleInputConfirm('v6')"
              @keyup.enter="handleInputConfirm('v6')"
            />
            <a-tag v-else style="background: #fff; borderStyle: dashed;" @click="showInput('inputVisible_v6')">
              <a-icon type="plus" /> 增加新选项
            </a-tag>
          </a-form-model-item>
          <a-form-model-item label="入住时间">
            <draggable v-model="tags_v7" group="tags_v7" @start="drag=true" @end="drag=false">
            <template v-for="(tag, index) in tags_v7">
              <a-tag :key="tag" color="orange" closable @close="() => handleClose(tag,'v7')">
                {{ tag }}
              </a-tag>
            </template>
            </draggable>
            <a-input
              v-if="inputVisible_v7"
              ref="input"
              type="text"
              size="small"
              :style="{ width: '78px' }"
              :value="inputValue_v7"
              @change="handleInputChange_v7"
              @blur="handleInputConfirm('v7')"
              @keyup.enter="handleInputConfirm('v7')"
            />
            <a-tag v-else style="background: #fff; borderStyle: dashed;" @click="showInput('inputVisible_v7')">
              <a-icon type="plus" /> 增加新选项
            </a-tag>
          </a-form-model-item>
          <a-button type="primary" @click="submitForm" block :loading="loading">提交</a-button>
        </a-form-model>
      </a-card>
    </a-tab-pane>
    <a-tab-pane key="2" tab="出租需求表单配置">
      Content of Tab Pane 3
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import JEditor from '@/components/jeecg/JEditor'
import { httpAction } from '@/api/manage'
import draggable from 'vuedraggable'
export default {
  components: {
    JEditor,
    draggable
  },
  props: [],
  data() {
    return {
      activeKey:"1",
      loading: false,
      formData: {
        phone: undefined,
        reply_verify: undefined,
        content: '',
        src: '',
        app_id: '',
        secret: ''
      },
      reply_verifyOptions: [
        {
          label: '开启',
          value: 2
        },
        {
          label: '关闭',
          value: 1
        }
      ],
      rules: {
        phone: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur'
          }
        ]
      },
      tags_v1: [],
      tags_v2: [],
      tags_v3: [],
      tags_v4: [],
      tags_v5: [],
      tags_v6: [],
      tags_v7: [],
      tags_v8: [],
      tags_v9: [],
      tags_v10: [],
      tags_v11: [],
      tags_v12: [],
      tags_v13: [],
      tags_v14: [],
      tags_v15: [],
      tags_v16: [],
      inputVisible_v1: false,
      inputVisible_v2: false,
      inputVisible_v3: false,
      inputVisible_v4: false,
      inputVisible_v5: false,
      inputVisible_v6: false,
      inputVisible_v7: false,
      inputVisible_v8: false,
      inputValue_v1: '',
      inputValue_v2: '',
      inputValue_v3: '',
      inputValue_v4: '',
      inputValue_v5: '',
      inputValue_v6: '',
      inputValue_v7: '',
      inputValue_v8: '',
    }
  },
  computed: {},
  watch: {},
  created() {
    // https://api.xiouxie.com
    httpAction('https://api.zkshlm.com/apiv2/rent/getRequestForm', '', 'post')
      .then(res => {
        let data = res.data 
        console.log(data[0].value)
        this.tags_v1 = data[0].value
        this.tags_v2= data[1].value
        this.tags_v3 = data[2].value
        this.tags_v4 = data[3].value
        this.tags_v5 = data[4].value
        this.tags_v6 = data[5].value
        this.tags_v7 = data[6].value
      })
      .finally(() => {})
  },
  mounted() {

  },
  methods: {
    submitForm() {
      const that = this
      that.loading = true
      let formData = {
        tags_v1:this.tags_v1,
        tags_v2:this.tags_v2,
        tags_v3:this.tags_v3,
        tags_v4:this.tags_v4,
        tags_v5:this.tags_v5,
        tags_v6:this.tags_v6,
        tags_v7:this.tags_v7,
      }
      httpAction('https://api.zkshlm.com/apiv2/rent/setRequestForm', formData, 'post')
        .then(res => {
          if (res.code == '200') {
            that.$message.success(res.msg)
          } else {
            that.$message.warning(res.msg)
          }
          that.loading = false
        })
        .finally(() => {
          that.loading = false
        })
    },
    handleClose(removedTag,item) {
      let name = 'tags_'+item
      const tags = this[name].filter(tag => tag !== removedTag)
      this[name] = tags
    },

    showInput(name) {
      this[name]= true
      this.$nextTick(function() {
        this.$refs.input.focus()
      })
    },

    handleInputChange_v1(e) {
      this.inputValue_v1 = e.target.value
    },
    handleInputChange_v2(e) {
      this.inputValue_v2 = e.target.value
    },
    handleInputChange_v3(e) {
      this.inputValue_v3 = e.target.value
    },
    handleInputChange_v4(e) {
      this.inputValue_v4 = e.target.value
    },
    handleInputChange_v5(e) {
      this.inputValue_v5 = e.target.value
    },
    handleInputChange_v6(e) {
      this.inputValue_v6 = e.target.value
    },
    handleInputChange_v7(e) {
      this.inputValue_v7 = e.target.value
    },
    handleInputConfirm(item) {
      // 定义
      let inputName = 'inputValue_' + item;
      let name = 'tags_' + item;
      let inputVisibleName = 'inputVisible_' + item;
      const inputValue = this[inputName]
      let tags = this[name]
      if (inputValue && tags.indexOf(inputValue) === -1) {
        tags = [...tags, inputValue]
      }
      Object.assign(this, {
        [name]:tags,
        [inputVisibleName]: false,
        [inputName]: ''
      })
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
