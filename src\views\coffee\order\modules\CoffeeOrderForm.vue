<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="订单类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type">
              <j-dict-select-tag type="radio" v-model="model.type" dictCode="order_type" placeholder="请选择订单类型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="门店收银台下单" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="substitute">
              <j-dict-select-tag type="radio" v-model="model.substitute" dictCode="coffee_substitute" placeholder="请选择门店收银台下单" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="预约" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="reserveTime">
              <a-input v-model="model.reserveTime" placeholder="请输入预约"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="商品" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="productId">
              <j-search-select-tag v-model="model.productId" dict="coffee_product,name,id"  />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status">
              <j-dict-select-tag type="radio" v-model="model.status" dictCode="coffee_status" placeholder="请选择状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remark">
              <a-input v-model="model.remark" placeholder="请输入备注"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否团购" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="temp">
              <j-dict-select-tag type="radio" v-model="model.temp" dictCode="coffee_temp" placeholder="请选择是否团购" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="核销时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="payTime">
              <j-date placeholder="请选择核销时间"  v-model="model.payTime" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="创建时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="createTime">
              <j-date placeholder="请选择创建时间"  v-model="model.createTime" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'CoffeeOrderForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
            type:"offline",
            substitute:0,
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           substitute: [
              { required: true, message: '请输入门店收银台下单!'},
           ],
           productId: [
              { required: true, message: '请输入商品!'},
           ],
           status: [
              { required: true, message: '请输入状态!'},
           ],
           temp: [
              { required: true, message: '请输入是否团购!'},
           ],
           createTime: [
              { required: true, message: '请输入创建时间!'},
           ],
        },
        url: {
          add: "/order/coffeeOrder/add",
          edit: "/order/coffeeOrder/edit",
          queryById: "/order/coffeeOrder/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>