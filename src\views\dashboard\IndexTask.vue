<template>
    <a-row :gutter="12">
      <a-card :loading="loading" :class="{ 'anty-list-cust':true }" :bordered="false" :style="{ marginTop: '24px' }">
        <a-tabs v-model="indexBottomTab" size="large" :tab-bar-style="{marginBottom: '24px', paddingLeft: '16px'}">
          <div class="extra-wrapper" slot="tabBarExtraContent">
            <a-radio-group v-model="indexRegisterType" @change="changeRegisterType">
              <a-radio-button value="转移登记">未付款</a-radio-button>
              <a-radio-button value="抵押登记">已付款</a-radio-button>
              <a-radio-button value="">所有</a-radio-button>
            </a-radio-group>
          </div>
          <a-tab-pane loading="true" tab="北京大学" key="1">
            <a-table :dataSource="dataSource1" size="default" rowKey="id" :columns="columns" :pagination="ipagination1" @change="tableChange1">
              <template slot="flowRate" slot-scope="text, record">
                <a-progress :strokeColor="getPercentColor(record.flowRate)" :format="getPercentFormat" :percent="getFlowRateNumber(record.flowRate)" style="width:80px" />
              </template>
            </a-table>
          </a-tab-pane>
          <a-tab-pane loading="true" tab="中关村国际大学" key="2">
            <a-table :dataSource="dataSource2" size="default" rowKey="id" :columns="columns2" :pagination="ipagination2" @change="tableChange2">
              <template slot="flowRate" slot-scope="text, record">
                <span style="color: red;">{{ record.flowRate }}小时</span>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </a-row>
</template>
<script>
  const dataCol1 = [{
    title: '收款人',
    align:"center",
    dataIndex: 'reBizCode'
  },{
    title: '收款时间',
    align:"center",
    dataIndex: 'type'
  },{
    title: '收款单位',
    align:"center",
    dataIndex: 'acceptBy'
  },{
    title: '金额',
    align:"center",
    dataIndex: 'acceptDate'
  },{
    title: '付款人',
    align:"center",
    dataIndex: 'curNode'
  },{
    title: '付款时间',
    align:"center",
    dataIndex: 'time'
  },{
    title: '付款单位',
    align:"center",
    dataIndex: 'unit'
  },{
    title: '金额',
    align:"center",
    dataIndex: 'flowRate'
  }];
  const dataSource1=[

  ]

  const dataCol2 = [{
    title: '收款人',
    align:"center",
    dataIndex: 'reBizCode'
  },{
    title: '收款时间',
    align:"center",
    dataIndex: 'type'
  },{
    title: '收款单位',
    align:"center",
    dataIndex: 'acceptBy'
  },{
    title: '金额',
    align:"center",
    dataIndex: 'acceptDate'
  },{
    title: '付款人',
    align:"center",
    dataIndex: 'curNode'
  },{
    title: '付款时间',
    align:"center",
    dataIndex: 'time'
  },{
    title: '付款单位',
    align:"center",
    dataIndex: 'unit'
  },{
    title: '金额',
    align:"center",
    dataIndex: 'flowRate'
  }];
  const dataSource2=[
    
  ]

  export default {
    name: "IndexBdc",
    components: {

    },
    data() {
      return {
        loading: true,

        dataSource1:[],
        dataSource2:[],
        columns:dataCol1,
        columns2:dataCol2,
        ipagination1:{
          current: 1,
          pageSize: 5,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + "-" + range[1] + " 共" + total + "条"
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0,

        },
        ipagination2:{
          current: 1,
          pageSize: 5,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + "-" + range[1] + " 共" + total + "条"
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0,
        },
        indexRegisterType:"转移登记",
        indexBottomTab:"1"

      }
    },
    methods:{
      changeRegisterType(e){
        console.log(e)
        this.indexRegisterType = e.target.value
        if(this.indexBottomTab=="1"){
          this.loadDataSource1()
        }else{
          this.loadDataSource2()
        }
      },
      tableChange1(pagination){
        this.ipagination1.current = pagination.current
        this.ipagination1.pageSize = pagination.pageSize
        this.queryTimeoutInfo()
      },
      tableChange2(pagination){
        this.ipagination2.current = pagination.current
        this.ipagination2.pageSize = pagination.pageSize
        this.queryNodeTimeoutInfo()
      },
      getFlowRateNumber(value){
        return Number(value)
      },
      getPercentFormat(value){
        if(value==100){
          return "超时"
        }else{
          return value+"%"
        }
      },
      getPercentColor(value){
        let p = Number(value)
        if(p>=90 && p<100){
          return 'rgb(244, 240, 89)'
        }else if(p>=100){
          return 'red'
        }else{
          return 'rgb(16, 142, 233)'
        }
      },

      loadDataSource1(){
        this.dataSource1 = dataSource1.filter(item=>{
          if(!this.indexRegisterType){
            return true
          }
          return item.type==this.indexRegisterType
        })
      },
      loadDataSource2(){
        this.dataSource2 = dataSource2.filter(item=>{
          if(!this.indexRegisterType){
            return true
          }
          return item.type==this.indexRegisterType
        })
      }
    },
    created() {
      this.loadDataSource1()
      this.loadDataSource2()
      setTimeout(() => {
        this.loading = !this.loading
      }, 1000)
    }
  }
</script>

<style lang="less" scoped>
  .extra-wrapper {
    line-height: 55px;
    padding-right: 24px;
  }


  .anty-list-cust {
    .ant-list-item-meta{flex: 0.3 !important;}
  }
  .anty-list-cust {
    .ant-list-item-content{flex:1 !important; justify-content:flex-start !important;margin-left: 20px;}
  }


</style>