<template>
  <a-card :bordered="false" :class="'cust-erp-sub-tab'">
    <!-- 操作按钮区域 -->
    <div class="table-operator" v-if="mainId">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('用户账单表')">导出</a-button>
      <a-upload
        name="file"
        :showUploadList="false"
        :multiple="false"
        :headers="tokenHeader"
        :action="importExcelUrl"
        @change="handleImportExcel">
          <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :scroll="{x:true}"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>

      </a-table>
    </div>

    <coffeeUserBill-modal ref="modalForm" @ok="modalFormOk" :mainId="mainId"></coffeeUserBill-modal>
  </a-card>
</template>

<script>

  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import CoffeeUserBillModal from './modules/CoffeeUserBillModal'

  export default {
    name: "CoffeeUserBillList",
    mixins:[JeecgListMixin],
    components: { CoffeeUserBillModal },
    props:{
      mainId:{
        type:String,
        default:'',
        required:false
      }
    },
    watch:{
      mainId:{
        immediate: true,
        handler(val) {
          if(!this.mainId){
            this.clearList()
          }else{
            this.queryParam['uid'] = val
            this.loadData(1);
          }
        }
      }
    },
    data () {
      return {
        description: '用户管理管理页面',
        disableMixinCreated:true,
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'用户',
            align:"center",
            dataIndex: 'uid_dictText',
          },
          {
            title:'类型',
            align:"center",
            dataIndex: 'pm_dictText',
          },
          {
            title:'账单标题',
            align:"center",
            dataIndex: 'title'
          },
          {
            title:'账单金额',
            align:"center",
            dataIndex: 'number'
          },
          {
            title:'剩余',
            align:"center",
            dataIndex: 'balance'
          },
          {
            title:'备注',
            align:"center",
            dataIndex: 'mark'
          },
          {
            title:'状态',
            align:"center",
            dataIndex: 'status_dictText',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' },
          }
        ],
        url: {
          list: "/user/coffeeUser/listCoffeeUserBillByMainId",
          delete: "/user/coffeeUser/deleteCoffeeUserBill",
          deleteBatch: "/user/coffeeUser/deleteBatchCoffeeUserBill",
          exportXlsUrl: "/user/coffeeUser/exportCoffeeUserBill",
          importUrl: "/user/coffeeUser/importCoffeeUserBill",
        },
        dictOptions:{
         isStatus:[],
         isRole:[],
        },
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl(){
        return `${window._CONFIG['domianURL']}/${this.url.importUrl}/${this.mainId}`;
      }
    },
    methods: {
      clearList(){
        this.dataSource=[]
        this.selectedRowKeys=[]
        this.ipagination.current = 1
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'nickname',text:'用户昵称',dictCode:''})
        fieldList.push({type:'string',value:'truename',text:'真实姓名',dictCode:''})
        fieldList.push({type:'string',value:'phone',text:'手机号',dictCode:''})
        fieldList.push({type:'int',value:'sex',text:'性别',dictCode:'zk_sex'})
        fieldList.push({type:'string',value:'headimgurl',text:'头像',dictCode:''})
        fieldList.push({type:'int',value:'isStatus',text:'状态',dictCode:'zk_status'})
        fieldList.push({type:'string',value:'isRole',text:'角色',dictCode:'zk_role'})
        fieldList.push({type:'BigDecimal',value:'money',text:'余额',dictCode:''})
        fieldList.push({type:'date',value:'createTime',text:'创建日期'})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>
