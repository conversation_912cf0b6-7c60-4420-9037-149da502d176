<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="店铺名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入店铺名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="首页轮播图" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-image-upload isMultiple v-decorator="['imgs']"></j-image-upload>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="店铺图片" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-image-upload isMultiple v-decorator="['img']"></j-image-upload>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['phone', validatorRules.phone]" placeholder="请输入联系电话"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="店铺地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['address']" placeholder="请输入店铺地址"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="店铺取货说明" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['store']" placeholder="请输入店铺取货说明"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="营业开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['startTime', validatorRules.startTime]" placeholder="请输入营业开始时间"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="营业结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['endTime', validatorRules.endTime]" placeholder="请输入营业结束时间"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="外送最小单数" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['num', validatorRules.num]" placeholder="请输入外送最小单数" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="预订时间间隔" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['timeNum', validatorRules.timeNum]" placeholder="请输入预订时间间隔" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="店铺备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-editor v-decorator="['remark',{trigger:'input'}]"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label=" 店铺状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['isStatus']" :trigger-change="true" dictCode="zk_status" placeholder="请选择 店铺状态"/>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JImageUpload from '@/components/jeecg/JImageUpload'
  import JDictSelectTag from "@/components/dict/JDictSelectTag"
  import JEditor from '@/components/jeecg/JEditor'

  export default {
    name: 'CoffeeShopForm',
    components: {
      JFormContainer,
      JImageUpload,
      JDictSelectTag,
      JEditor,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
          name: {
            rules: [
              { required: true, message: '请输入店铺名称!'},
              { validator: (rule, value, callback) => validateDuplicateValue('coffee_shop', 'name', value, this.model.id, callback)},
            ]
          },
          phone: {
            rules: [
              { required: false},
              { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码!'},
            ]
          },
          startTime: {
            rules: [
              { required: true, message: '请输入营业开始时间!'},
            ]
          },
          endTime: {
            rules: [
              { required: true, message: '请输入营业结束时间!'},
            ]
          },
          num: {
            rules: [
              { required: true, message: '请输入外送最小单数!'},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
            ]
          },
          timeNum: {
            rules: [
              { required: true, message: '请输入预订时间间隔!'},
            ]
          },
          remark: {
            rules: [
              { required: true, message: '请输入店铺备注!'},
            ]
          },
        },
        url: {
          add: "/shop/coffeeShop/add",
          edit: "/shop/coffeeShop/edit",
          queryById: "/shop/coffeeShop/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'name','imgs','img','phone','address','store','startTime','endTime','num','timeNum','remark','isStatus'))
        })
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          });
        }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            let formData = Object.assign(this.model, values);
            console.log("表单提交数据",formData)
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'name','imgs','img','phone','address','store','startTime','endTime','num','timeNum','remark','isStatus'))
      },
    }
  }
</script>