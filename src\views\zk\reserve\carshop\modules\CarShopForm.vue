<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <!-- 页面载入后，调用init函数 -->
          <div id="container" v-bind:class="{ active: isActive, noshow:isShow}"></div>
          <a-col :span="24">
            <a-form-item label="名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入名称"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
              <a-form-item label="地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-decorator="['address']" placeholder="请输入地址" disabled  style="width: 200px;"></a-input>
                <a-button type="primary" icon="arrow-down"  v-on:click="admap()" />
              </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="价格" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['price', validatorRules.price]" placeholder="请输入价格" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="客服联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['phone']" placeholder="请输入客服联系电话"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="营业开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-time placeholder="请选择营业开始时间" v-decorator="['startTime', validatorRules.startTime]" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="营业结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-time placeholder="请选择营业结束时间" v-decorator="['startEnd', validatorRules.startEnd]" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="午休开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-time placeholder="请选择午休开始时间" v-decorator="['lunchStart', validatorRules.lunchStart]" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="午休结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-time placeholder="请选择午休结束时间" v-decorator="['lunchEnd', validatorRules.lunchEnd]" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="单位时间段" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['block', validatorRules.block]" placeholder="请输入单位时间段"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="时间段服务个数" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['gauge', validatorRules.gauge]" placeholder="请输入时间段服务个数"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['isStatus', validatorRules.isStatus]" :trigger-change="true" dictCode="zk_status" placeholder="请选择状态" />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'CarShopForm',
    components: {
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        isShow:false,
        isActive:false,
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
          name: {
            rules: [
              { required: true, message: '请输入名称!'},
            ]
          },
          address: {
            rules: [
              { required: true, message: '请输入地址!'},
            ]
          },
          price: {
            rules: [
              { required: true, message: '请输入价格!'},
            ]
          },
          startTime: {
            initialValue:"08:30:00",
            rules: [
              { required: true, message: '请输入营业开始时间!'},
            ]
          },
          startEnd: {
            initialValue:"17:50:00",
            rules: [
              { required: true, message: '请输入营业结束时间!'},
            ]
          },
          lunchStart: {
            initialValue:"12:30:00",
            rules: [
              { required: true, message: '请输入午休开始时间!'},
            ]
          },
          lunchEnd: {
            initialValue:"13:30:00",
            rules: [
              { required: true, message: '请输入午休结束时间!'},
            ]
          },
          block: {
            initialValue:"60",
            rules: [
              { required: true, message: '请输入单位时间段!'},
            ]
          },
          gauge: {
            initialValue:"4",
            rules: [
              { required: true, message: '请输入时间段服务个数!'},
            ]
          },
          isStatus: {
            initialValue:0,
            rules: [
              { required: true, message: '请输入状态!'},
            ]
          },
        },
        url: {
          add: "/carshop/carShop/add",
          edit: "/carshop/carShop/edit",
          queryById: "/carshop/carShop/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
      let script = document.createElement("script");
      //设置标签的type属性
      script.type = "text/javascript";
      //设置标签的链接地址
      script.src = "https://map.qq.com/api/gljs?v=1.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77";
      //添加标签到dom
      document.body.appendChild(script);
    },
    methods: {
      admap(){
        var that = this;
        if(!that.isActive) {
          //样式显示
          that.isShow = false;
          that.isActive = true;
          if(!that.isDeduplication_map) {
            //定义地图中心点坐标
            var center = new TMap.LatLng(39.9886834,116.3212977)
            //定义map变量，调用 TMap.Map() 构造函数创建地图
            var map = new TMap.Map(document.getElementById('container'), {
                center: center,//设置地图中心点坐标
            }); 
            //坐标设置
            //初始化marker图层
            var markerLayer = new TMap.MultiMarker({
                id: 'marker-layer',
                map: map
            });
            //定义坐标拾取器
            var clickHandler=function(evt){
                var lat = evt.latLng.getLat().toFixed(7);
                var lng = evt.latLng.getLng().toFixed(7);
                that.model.address = lat + "," + lng;
                that.$nextTick(() => {
                  that.form.setFieldsValue(pick(that.model,'address'))
                })
                markerLayer.setGeometries([])
                markerLayer.add({
                  position: evt.latLng
                });   
                console.log("您点击的的坐标是："+ lat + "," + lng);
            }
            //Map实例创建后，通过on方法绑定点击事件
            map.on("click",clickHandler)
            that.isDeduplication_map = true;
          }
        } else {
          that.isShow = true;
          that.isActive = false;
        }
      },
      // 地图结束
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'name','address','price','phone','startTime','startEnd','lunchStart','lunchEnd','block','gauge','isStatus'))
        })
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          });
        }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            let formData = Object.assign(this.model, values);
            console.log("表单提交数据",formData)
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'name','address','price','phone','startTime','startEnd','lunchStart','lunchEnd','block','gauge','isStatus'))
      },
    }
  }
</script>