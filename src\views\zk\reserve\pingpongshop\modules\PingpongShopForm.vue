<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
              <a-input v-model="model.name" placeholder="请输入名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address">
              <a-input v-model="model.address" placeholder="请输入地址"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="客服联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="phone">
              <a-input v-model="model.phone" placeholder="请输入客服联系电话"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="非营业天" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="weekTime">
              <j-multi-select-tag type="checkbox" v-model="model.weekTime" dictCode="week" placeholder="请选择非营业天" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label=" 营业开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="startTime">
              <j-time placeholder="请选择 营业开始时间"  v-model="model.startTime" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label=" 营业结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="startEnd">
              <j-time placeholder="请选择 营业结束时间"  v-model="model.startEnd" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="午休开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="lunchStart">
              <j-time placeholder="请选择午休开始时间"  v-model="model.lunchStart" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="午休结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="lunchEnd">
              <j-time placeholder="请选择午休结束时间"  v-model="model.lunchEnd" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="晚餐开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dinnerStart">
              <j-time placeholder="请选择晚餐开始时间"  v-model="model.dinnerStart" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="晚餐结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dinnerEnd">
              <j-time placeholder="请选择晚餐结束时间"  v-model="model.dinnerEnd" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="单位时间段" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="block">
              <a-input v-model="model.block" placeholder="请输入单位时间段"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="时间段服务个数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="gauge">
              <a-input v-model="model.gauge" placeholder="请输入时间段服务个数"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isStatus">
              <j-dict-select-tag type="radio" v-model="model.isStatus" dictCode="zk_status" placeholder="请选择状态" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'PingpongShopForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
            block:"60",
            gauge:"4",
            isStatus:0,
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           name: [
              { required: true, message: '请输入名称!'},
           ],
        },
        url: {
          add: "/pingpongshop/pingpongShop/add",
          edit: "/pingpongshop/pingpongShop/edit",
          queryById: "/pingpongshop/pingpongShop/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>