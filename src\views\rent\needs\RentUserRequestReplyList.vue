<template>
  <a-card :bordered="false" :class="'cust-erp-sub-tab'">
    <!-- 操作按钮区域 -->
    <div class="table-operator" v-if="mainId">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('需求回复管理')">导出</a-button>
      <a-upload
        name="file"
        :showUploadList="false"
        :multiple="false"
        :headers="tokenHeader"
        :action="importExcelUrl"
        @change="handleImportExcel">
          <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :scroll="{x:true}"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>

      </a-table>
    </div>

    <rentUserRequestReply-modal ref="modalForm" @ok="modalFormOk" :mainId="mainId"></rentUserRequestReply-modal>
  </a-card>
</template>

<script>

  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import RentUserRequestReplyModal from './modules/RentUserRequestReplyModal'

  export default {
    name: "RentUserRequestReplyList",
    mixins:[JeecgListMixin],
    components: { RentUserRequestReplyModal },
    props:{
      mainId:{
        type:String,
        default:'',
        required:false
      }
    },
    watch:{
      mainId:{
        immediate: true,
        handler(val) {
          if(!this.mainId){
            this.clearList()
          }else{
            this.queryParam['pid'] = val
            this.loadData(1);
          }
        }
      }
    },
    data () {
      return {
        description: '用户需求管理管理页面',
        disableMixinCreated:true,
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'内容',
            align:"center",
            dataIndex: 'content_dictText',
          },
          {
            title:'用户名',
            align:"center",
            dataIndex: 'name'
          },
          {
            title:'推荐房源',
            align:"center",
            dataIndex: 'rentId_dictText',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' },
          }
        ],
        url: {
          list: "/needs/rentUserRequest/listRentUserRequestReplyByMainId",
          delete: "/needs/rentUserRequest/deleteRentUserRequestReply",
          deleteBatch: "/needs/rentUserRequest/deleteBatchRentUserRequestReply",
          exportXlsUrl: "/needs/rentUserRequest/exportRentUserRequestReply",
          importUrl: "/needs/rentUserRequest/importRentUserRequestReply",
        },
        dictOptions:{
         category:[],
         status:[],
        },
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl(){
        return `${window._CONFIG['domianURL']}/${this.url.importUrl}/${this.mainId}`;
      }
    },
    methods: {
      clearList(){
        this.dataSource=[]
        this.selectedRowKeys=[]
        this.ipagination.current = 1
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'datetime',value:'createTime',text:'创建日期'})
        fieldList.push({type:'int',value:'category',text:'类型',dictCode:'rent_category'})
        fieldList.push({type:'sel_search',value:'uid',text:'用户',dictTable:'rent_user', dictText:'name', dictCode:'id'})
        fieldList.push({type:'string',value:'content',text:'内容',dictCode:''})
        fieldList.push({type:'string',value:'nickname',text:'姓名',dictCode:''})
        fieldList.push({type:'string',value:'phone',text:'手机号',dictCode:''})
        fieldList.push({type:'string',value:'unit',text:'单位',dictCode:''})
        fieldList.push({type:'string',value:'tagsV1',text:'朝向',dictCode:''})
        fieldList.push({type:'string',value:'tagsV2',text:'面积',dictCode:''})
        fieldList.push({type:'string',value:'tagsV3',text:'楼层',dictCode:''})
        fieldList.push({type:'string',value:'tagsV4',text:'租期',dictCode:''})
        fieldList.push({type:'string',value:'tagsV5',text:'电梯',dictCode:''})
        fieldList.push({type:'string',value:'tagsV6',text:'供暖',dictCode:''})
        fieldList.push({type:'string',value:'tagsV7',text:'入驻时间',dictCode:''})
        fieldList.push({type:'int',value:'status',text:'状态',dictCode:'rent_status'})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>
