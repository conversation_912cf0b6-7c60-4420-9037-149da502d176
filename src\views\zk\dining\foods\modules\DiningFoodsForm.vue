<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <!-- 主表单区域 -->
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24" >
            <a-form-item label="名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入名称" ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="总价" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['price', validatorRules.price]" placeholder="请输入总价" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="排序" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['sort', validatorRules.sort]" placeholder="请输入排序" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="显示时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择显示时间" v-decorator="['onlineTime', validatorRules.onlineTime]" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="上线时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择上线时间" v-decorator="['startTime', validatorRules.startTime]" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择结束时间" v-decorator="['endTime', validatorRules.endTime]" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['isStatus', validatorRules.isStatus]" :trigger-change="true" dictCode="zk_status" placeholder="请选择状态" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
      <!-- 子表单区域 -->
    <a-tabs v-model="activeKey" @change="handleChangeTabs">
      <a-tab-pane tab="套餐详情" :key="refKeys[0]" :forceRender="true">
        <j-editable-table
          :ref="refKeys[0]"
          :loading="diningFoodTable.loading"
          :columns="diningFoodTable.columns"
          :dataSource="diningFoodTable.dataSource"
          :maxHeight="300"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :actionButton="true"/>
      </a-tab-pane>
      <a-tab-pane tab="套餐标识" :key="refKeys[1]" :forceRender="true">
        <j-editable-table
          :ref="refKeys[1]"
          :loading="diningMarkTable.loading"
          :columns="diningMarkTable.columns"
          :dataSource="diningMarkTable.dataSource"
          :maxHeight="300"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :actionButton="true"/>
      </a-tab-pane>
    </a-tabs>
  </a-spin>
</template>

<script>

  import pick from 'lodash.pick'
  import { getAction } from '@/api/manage'
  import { FormTypes,getRefPromise } from '@/utils/JEditableTableUtil'
  import { JEditableTableMixin } from '@/mixins/JEditableTableMixin'
  import { validateDuplicateValue } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDate from '@/components/jeecg/JDate'  
  import JDictSelectTag from "@/components/dict/JDictSelectTag"

  export default {
    name: 'DiningFoodsForm',
    mixins: [JEditableTableMixin],
    components: {
      JFormContainer,
      JDate,
      JDictSelectTag,
    },
    data() {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        labelCol2: {
          xs: { span: 24 },
          sm: { span: 3 },
        },
        wrapperCol2: {
          xs: { span: 24 },
          sm: { span: 20 },
        },
        // 新增时子表默认添加几行空数据
        addDefaultRowNum: 1,
        validatorRules: {
          name: {
            rules: [
              { required: true, message: '请输入名称!'},
              { validator: (rule, value, callback) => validateDuplicateValue('dining_foods', 'name', value, this.model.id, callback)},
            ]
          },
          price: {
            initialValue:0.00,
            rules: [
              { required: true, message: '请输入总价!'},
              { pattern: /^(([1-9][0-9]*)|([0]\.\d{0,2}|[1-9][0-9]*\.\d{0,2}))$/, message: '请输入正确的金额!'},
            ]
          },
          sort: {
            initialValue:10,
            rules: [
              { required: true, message: '请输入排序!'},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
            ]
          },
          onlineTime: {
            rules: [
              { required: true, message: '请输入显示时间!'},
            ]
          },
          startTime: {
            rules: [
              { required: true, message: '请输入上线时间!'},
            ]
          },
          endTime: {
            rules: [
              { required: true, message: '请输入结束时间!'},
            ]
          },
          isStatus: {
            initialValue:"0",
            rules: [
              { required: true, message: '请输入状态!'},
            ]
          },
        },
        refKeys: ['diningFood', 'diningMark', ],
        tableKeys:['diningFood', 'diningMark', ],
        activeKey: 'diningFood',
        // 套餐详情
        diningFoodTable: {
          loading: false,
          dataSource: [],
          columns: [
            {
              title: '商品名称',
              key: 'name',
              type: FormTypes.sel_search,
              dictCode:"dining_product,name,id",
              width:"200px",
              placeholder: '请输入${title}',
              defaultValue:'',
              validateRules: [{ required: true, message: '${title}不能为空' }],
            },
            {
              title: '排序',
              key: 'sort',
              type: FormTypes.inputNumber,
              width:"200px",
              placeholder: '请输入${title}',
              defaultValue:10,
              validateRules: [{ required: true, message: '${title}不能为空' }],
            },
          ]
        },
        // 套餐标识
        diningMarkTable: {
          loading: false,
          dataSource: [],
          columns: [
            {
              title: '分类',
              key: 'cateId',
              type: FormTypes.sel_search,
              dictCode:"dining_category,name,id",
              width:"200px",
              placeholder: '请输入${title}',
              defaultValue:'',
              validateRules: [{ required: true, message: '${title}不能为空' }],
            },
            {
              title: '必选数',
              key: 'max',
              type: FormTypes.inputNumber,
              width:"200px",
              placeholder: '请输入${title}',
              defaultValue:'',
              validateRules: [{ required: true, message: '${title}不能为空' }],
            },
          ]
        },
        url: {
          add: "/foods/diningFoods/add",
          edit: "/foods/diningFoods/edit",
          queryById: "/foods/diningFoods/queryById",
          diningFood: {
            list: '/foods/diningFoods/queryDiningFoodByMainId'
          },
          diningMark: {
            list: '/foods/diningFoods/queryDiningMarkByMainId'
          },
        }
      }
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：false流程表单 true普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    methods: {
      addBefore(){
        this.form.resetFields()
        this.diningFoodTable.dataSource=[]
        this.diningMarkTable.dataSource=[]
      },
      getAllTable() {
        let values = this.tableKeys.map(key => getRefPromise(this, key))
        return Promise.all(values)
      },
      /** 调用完edit()方法之后会自动调用此方法 */
      editAfter() {
        let fieldval = pick(this.model,'name','price','sort','onlineTime','startTime','endTime','isStatus')
        this.$nextTick(() => {
          this.form.setFieldsValue(fieldval)
        })
        // 加载子表数据
        if (this.model.id) {
          let params = { id: this.model.id }
          this.requestSubTableData(this.url.diningFood.list, params, this.diningFoodTable)
          this.requestSubTableData(this.url.diningMark.list, params, this.diningMarkTable)
        }
      },
      /** 整理成formData */
      classifyIntoFormData(allValues) {
        let main = Object.assign(this.model, allValues.formValue)
        return {
          ...main, // 展开
          diningFoodList: allValues.tablesValue[0].values,
          diningMarkList: allValues.tablesValue[1].values,
        }
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          })
        }
      },
      validateError(msg){
        this.$message.error(msg)
      },
     popupCallback(row){
       this.form.setFieldsValue(pick(row,'name','price','sort','onlineTime','startTime','endTime','isStatus'))
     },

    }
  }
</script>

<style scoped>
</style>