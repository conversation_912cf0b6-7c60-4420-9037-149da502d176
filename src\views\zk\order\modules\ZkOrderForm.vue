<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="用户" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['userid', validatorRules.userid]" dict="zk_user,truename,id" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="线路" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['lineid', validatorRules.lineid]" dict="zk_line,name,id" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="微信订单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['wxordernum']" placeholder="请输入微信订单号"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="预订时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择预订时间" v-decorator="['starttime', validatorRules.starttime]" :trigger-change="true" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="上车站点" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['startSiteid', validatorRules.startSiteid]" dict="zk_site,name,id" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="下车站点" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['stopSiteid', validatorRules.stopSiteid]" dict="zk_site,name,id" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="用户手机号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['phone', validatorRules.phone]" placeholder="请输入用户手机号"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="支付金额" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['payPrice', validatorRules.payPrice]" placeholder="请输入支付金额" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="原价" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['price', validatorRules.price]" placeholder="请输入原价" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="支付状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['payStatus', validatorRules.payStatus]" :trigger-change="true" dictCode="zk_pay" placeholder="请选择支付状态"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="订单状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['status', validatorRules.status]" :trigger-change="true" dictCode="zk_order_status" placeholder="请选择订单状态"/>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDate from '@/components/jeecg/JDate'  
  import JDictSelectTag from "@/components/dict/JDictSelectTag"
  import JSearchSelectTag from '@/components/dict/JSearchSelectTag'

  export default {
    name: 'ZkOrderForm',
    components: {
      JFormContainer,
      JDate,
      JDictSelectTag,
      JSearchSelectTag,
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
          userid: {
            rules: [
              { required: true, message: '请输入用户!'},
            ]
          },
          lineid: {
            rules: [
              { required: true, message: '请输入线路!'},
            ]
          },
          starttime: {
            rules: [
              { required: true, message: '请输入预订时间!'},
            ]
          },
          startSiteid: {
            rules: [
              { required: true, message: '请输入上车站点!'},
            ]
          },
          stopSiteid: {
            rules: [
              { required: true, message: '请输入下车站点!'},
            ]
          },
          phone: {
            rules: [
              { required: true, message: '请输入用户手机号!'},
            ]
          },
          payPrice: {
            rules: [
              { required: true, message: '请输入支付金额!'},
            ]
          },
          price: {
            rules: [
              { required: true, message: '请输入原价!'},
            ]
          },
          payStatus: {
            rules: [
              { required: true, message: '请输入支付状态!'},
            ]
          },
          status: {
            rules: [
              { required: true, message: '请输入订单状态!'},
            ]
          },
        },
        url: {
          add: "/order/zkOrder/add",
          edit: "/order/zkOrder/edit",
          queryById: "/order/zkOrder/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'userid','lineid','wxordernum','starttime','startSiteid','stopSiteid','phone','payPrice','price','payStatus','status','createTime'))
        })
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          });
        }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            let formData = Object.assign(this.model, values);
            console.log("表单提交数据",formData)
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'userid','lineid','wxordernum','starttime','startSiteid','stopSiteid','phone','payPrice','price','payStatus','status','createTime'))
      },
    }
  }
</script>