<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="用户">
              <j-search-select-tag placeholder="请选择用户" v-model="queryParam.uid" dict="dining_user,truename,id"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="取餐地址">
              <j-dict-select-tag placeholder="请选择取餐地址" v-model="queryParam.address" dictCode="dining_address"/>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="状态">
                <j-dict-select-tag placeholder="请选择状态" v-model="queryParam.status" dictCode="zk_order"/>
              </a-form-item>
            </a-col>
            <a-col :xl="10" :lg="11" :md="12" :sm="24">
              <a-form-item label="创建时间">
                <j-date :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择开始时间" class="query-group-cust" v-model="queryParam.createTime_begin"></j-date>
                <span class="query-group-split-cust"></span>
                <j-date :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择结束时间" class="query-group-cust" v-model="queryParam.createTime_end"></j-date>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->
    
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('订单管理')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        class="j-table-force-nowrap"
        :scroll="{x:true}"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type:'radio'}"
        :customRow="clickThenSelect"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <a-tabs defaultActiveKey="1">
      <a-tab-pane tab="订单附表" key="1" >
        <DiningOrderAttrList :mainId="selectedMainId" />
      </a-tab-pane>
    </a-tabs>

    <diningOrder-modal ref="modalForm" @ok="modalFormOk"></diningOrder-modal>
  </a-card>
</template>

<script>

  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import DiningOrderModal from './modules/DiningOrderModal'
  import { getAction } from '@/api/manage'
  import DiningOrderAttrList from './DiningOrderAttrList'
  import {initDictOptions,filterMultiDictText} from '@/components/dict/JDictSelectUtil'
  import '@/assets/less/TableExpand.less'

  export default {
    name: "DiningOrderList",
    mixins:[JeecgListMixin],
    components: {
      DiningOrderAttrList,
      DiningOrderModal
    },
    data () {
      return {
        description: '订单管理管理页面',
        // 表头
        columns: [
          {
            title:'用户',
            align:"center",
            dataIndex: 'uid_dictText',
          },
          {
            title:'取餐地址',
            align:"center",
            dataIndex: 'address_dictText',
          },
          {
            title:'商户订单号',
            align:"center",
            dataIndex: 'ordernum'
          },
          {
            title:'微信订单号',
            align:"center",
            dataIndex: 'wxOrdernum'
          },
          {
            title:'商品',
            align:"center",
            dataIndex: 'productId_dictText',
          },
          {
            title:'套餐',
            align:"center",
            dataIndex: 'foodsId_dictText',
          },
          {
            title:'预约时间',
            align:"center",
            sorter: true,
            dataIndex: 'time',
            customRender:function (text) {
              return !text?"":(text.length>10?text.substr(0,10):text)
            }
          },
          {
            title:'联系电话',
            align:"center",
            dataIndex: 'phone'
          },
          {
            title:'数量',
            align:"center",
            dataIndex: 'num'
          },
          {
            title:'单价',
            align:"center",
            dataIndex: 'price'
          },
          {
            title:'总价',
            align:"center",
            dataIndex: 'payPrice'
          },
          {
            title:'状态',
            align:"center",
            dataIndex: 'status_dictText',
          },
          {
            title:'备注',
            align:"center",
            dataIndex: 'remark'
          },
          {
            title:'支付时间',
            align:"center",
            dataIndex: 'payTime'
          },
          {
            title:'创建时间',
            align:"center",
            sorter: true,
            dataIndex: 'createTime'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' },
          }
        ],
        url: {
          list: "/order/diningOrder/list",
          delete: "/order/diningOrder/delete",
          deleteBatch: "/order/diningOrder/deleteBatch",
          exportXlsUrl: "/order/diningOrder/exportXls",
          importExcelUrl: "order/diningOrder/importExcel",
        },
        dictOptions:{
         address:[],
         status:[],
        },
        /* 分页参数 */
        ipagination:{
          current: 1,
          pageSize: 5,
          pageSizeOptions: ['5', '10', '50'],
          showTotal: (total, range) => {
            return range[0] + "-" + range[1] + " 共" + total + "条"
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        selectedMainId:'',
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      }
    },
    methods: {
      initDictConfig(){
        initDictOptions('dining_user,truename,id').then((res) => {
          if (res.success) {
            this.$set(this.dictOptions, 'uid', res.result)
          }
        })
        initDictOptions('dining_address').then((res) => {
          if (res.success) {
            this.$set(this.dictOptions, 'address', res.result)
          }
        })
        initDictOptions('dining_product,name,id').then((res) => {
          if (res.success) {
            this.$set(this.dictOptions, 'productId', res.result)
          }
        })
        initDictOptions('dining_foods,name,id').then((res) => {
          if (res.success) {
            this.$set(this.dictOptions, 'foodsId', res.result)
          }
        })
        initDictOptions('zk_order').then((res) => {
          if (res.success) {
            this.$set(this.dictOptions, 'status', res.result)
          }
        })
      },
      clickThenSelect(record) {
        return {
          on: {
            click: () => {
              this.onSelectChange(record.id.split(","), [record]);
            }
          }
        }
      },
      onClearSelected() {
        this.selectedRowKeys = [];
        this.selectionRows = [];
        this.selectedMainId=''
      },
      onSelectChange(selectedRowKeys, selectionRows) {
        this.selectedMainId=selectedRowKeys[0]
        this.selectedRowKeys = selectedRowKeys;
        this.selectionRows = selectionRows;
      },
      loadData(arg) {
        if(!this.url.list){
          this.$message.error("请设置url.list属性!")
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1;
        }
        this.onClearSelected()
        var params = this.getQueryParams();//查询条件
        this.loading = true;
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            this.dataSource = res.result.records;
            this.ipagination.total = res.result.total;
          }
          if(res.code===510){
            this.$message.warning(res.message)
          }
          this.loading = false;
        })
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'sel_search',value:'uid',text:'用户',dictTable:'dining_user', dictText:'truename', dictCode:'id'})
        fieldList.push({type:'int',value:'address',text:'取餐地址',dictCode:'dining_address'})
        fieldList.push({type:'string',value:'ordernum',text:'商户订单号',dictCode:''})
        fieldList.push({type:'string',value:'wxOrdernum',text:'微信订单号',dictCode:''})
        fieldList.push({type:'sel_search',value:'productId',text:'商品',dictTable:'dining_product', dictText:'name', dictCode:'id'})
        fieldList.push({type:'sel_search',value:'foodsId',text:'套餐',dictTable:'dining_foods', dictText:'name', dictCode:'id'})
        fieldList.push({type:'date',value:'time',text:'预约时间'})
        fieldList.push({type:'string',value:'phone',text:'联系电话',dictCode:''})
        fieldList.push({type:'int',value:'num',text:'数量',dictCode:''})
        fieldList.push({type:'BigDecimal',value:'price',text:'单价',dictCode:''})
        fieldList.push({type:'BigDecimal',value:'payPrice',text:'总价',dictCode:''})
        fieldList.push({type:'int',value:'status',text:'状态',dictCode:'zk_order'})
        fieldList.push({type:'string',value:'remark',text:'备注',dictCode:''})
        fieldList.push({type:'datetime',value:'payTime',text:'支付时间'})
        fieldList.push({type:'datetime',value:'createTime',text:'创建时间'})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>