<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row>
          <!-- 页面载入后，调用init函数 -->
          <div id="container" v-bind:class="{ active: isActive, noshow:isShow}"></div>
          <a-col :span="24">
            <a-form-item label="站点名" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入站点名"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="价格" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['price', validatorRules.price]" placeholder="请输入价格"></a-input>
            </a-form-item>
          </a-col>
		  <a-col :span="24">
		    <a-form-item label="折扣价" :labelCol="labelCol" :wrapperCol="wrapperCol">
		      <a-input-number v-decorator="['discountPrice']" placeholder="请输入折扣价" style="width: 100%"/>
		    </a-form-item>
		  </a-col>
          <a-col :span="24">
            <a-form-item label="停站时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-time-picker   format="HH:mm"  @change="onChange">
                <a-icon slot="suffixIcon" type="smile" />
              </a-time-picker>
                <a-input   v-decorator="['stoptime']"   disabled style="width: 200px;"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['remarks']" placeholder="请输入备注"></a-input>
            </a-form-item>
          </a-col>
         
         <a-col :span="24">
             <a-form-item label="地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
               <a-input v-decorator="['address']" placeholder="请输入地址" disabled  style="width: 200px;"></a-input>
               <a-button type="primary" icon="arrow-down"  v-on:click="admap()" />
             </a-form-item>
          </a-col>
          
          <a-col :span="24">
            <a-form-item label="排序" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['sort', validatorRules.sort]" placeholder="请输入排序"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['isStatus', validatorRules.isStatus]" :trigger-change="true" dictCode="zk_status" placeholder="请选择状态"/>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>

  </j-modal>
</template>

<script>
  import moment from 'moment';
  import { httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateBusinessValue } from '@/utils/util'
  import JDictSelectTag from "@/components/dict/JDictSelectTag"
  export default {
    name: "ZkSiteModal",
    components: {
      JDictSelectTag,
    },
    props:{
      mainId:{
        type:String,
        required:false,
        default:''
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        isShow:false,
        isActive:false,
        isDeduplication_map:false,
        address:'',
        title:"操作",
        width:800,
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },

        confirmLoading: false,
        validatorRules: {
          name: {
            rules: [
              { required: true, message: '请输入站点名!'},
            ]
          },
          price: {
            rules: [
              { required: true, message: '请输入价格!'},
              { pattern: /^(([1-9][0-9]*)|([0]\.\d{0,2}|[1-9][0-9]*\.\d{0,2}))$/, message: '请输入正确的金额!'},
            ]
          },
		  discountPrice: {
		    rules: [
		      { required: false, message: '请输入折扣价!'},
		      { pattern: /^(([1-9][0-9]*)|([0]\.\d{0,2}|[1-9][0-9]*\.\d{0,2}))$/, message: '请输入正确的金额!'},
		    ]
		  },
          sort: {
            rules: [
              { required: false},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
            ]
          },
          isStatus: {
            rules: [
              { required: true, message: '请输入状态!'},
            ]
          },
        },
        url: {
          add: "/line/zkLine/addZkSite",
          edit: "/line/zkLine/editZkSite",
        }

      }
    },
    created () {
      let script = document.createElement("script");
      //设置标签的type属性
      script.type = "text/javascript";
      //设置标签的链接地址
      script.src = "https://map.qq.com/api/gljs?v=1.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77";
      //添加标签到dom
      document.body.appendChild(script);
    },
    methods: {
      moment,
      onChange(time, timeString) {
        this.model.stoptime = timeString;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'stoptime'))
        })
      },
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'name','price','discountPrice','stoptime','remarks','address','sort','isStatus'))
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            let formData = Object.assign(this.model, values);
            formData['lineId'] = this.mainId
            console.log("表单提交数据",formData)
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }

        })
      },
      handleCancel () {
        this.close()
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'createBy','createTime','updateBy','updateTime','sysOrgCode','lineId','name','discountPrice','price','stoptime','remarks','address','sort','isStatus'))
      },
      admap(){
        var that = this;
        if(!that.isActive) {
          //样式显示
          that.isShow = false;
          that.isActive = true;
          if(!that.isDeduplication_map) {
            //定义地图中心点坐标
            var center = new TMap.LatLng(39.984120, 116.307484)
            //定义map变量，调用 TMap.Map() 构造函数创建地图
            var map = new TMap.Map(document.getElementById('container'), {
                center: center,//设置地图中心点坐标
            }); 
            //坐标设置
            //初始化marker图层
            var markerLayer = new TMap.MultiMarker({
                id: 'marker-layer',
                map: map
            });
            //定义坐标拾取器
            var clickHandler=function(evt){
                var lat = evt.latLng.getLat().toFixed(7);
                var lng = evt.latLng.getLng().toFixed(7);
                that.model.address = lat + "," + lng;
                that.$nextTick(() => {
                  that.form.setFieldsValue(pick(that.model,'address'))
                })
                markerLayer.setGeometries([])
                markerLayer.add({
                  position: evt.latLng
                });   
                console.log("您点击的的坐标是："+ lat + "," + lng);
            }
            //Map实例创建后，通过on方法绑定点击事件
            map.on("click",clickHandler)
            that.isDeduplication_map = true;
          }
        } else {
          that.isShow = true;
          that.isActive = false;
        }
      }
    }
  }
  
</script>
<style>
  .container{
      /*地图(容器)显示大小*/
      width:100%;
      height:400px;
  }
  .noshow{
    display: none;
  }
</style>