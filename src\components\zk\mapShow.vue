<template>
    <div>
        <!-- 页面载入后，调用init函数 -->
        <div id="container"></div>
    </div>
</template>
<script>

export default {
    name: 'mapshow',
    props: {
        // address:address, // 地址数组
        dataSource: {
            type: Array,
            default: []
        }
    },
    data() {
      return {
        // 内部使用的 slots ，不再处理
        address:[],
        map:''
      }
    },
    created() {
        //加入地图点信息
        this.list()
    },
    methods: {
        list() {
            if(this.map != '') {
                //销毁地图
                this.map.destroy();
            } else {
                let script = document.createElement("script");
                //设置标签的type属性
                script.type = "text/javascript";
                //设置标签的链接地址
                script.src = "https://map.qq.com/api/gljs?v=1.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77";
                //添加标签到dom
                document.body.appendChild(script);
            }
            if(this.dataSource.length > 0) {
                this.init()
            }
        },
        init() {
            setTimeout(() => {
                //获取地图点
                let data = []
                let i = 1
                this.dataSource.forEach(function(v) {
                    let arr = {}
                    arr.id = v.id
                    arr.styleId = 'label'
                    let lat = v.address.split(',')[0]
                    let lng = v.address.split(',')[1]
                    arr.position = new TMap.LatLng(lat,lng)
                    delete arr.position.height
                    arr.content = v.name
                    arr.properties = {
                        'title':"marker".i
                    }
                    i++
                    data.push(arr)
                })
                this.address = data
                //定义地图中心点坐标
                var center = new TMap.LatLng(40.040074, 116.273519)
                //定义map变量，调用 TMap.Map() 构造函数创建地图
                var map = new TMap.Map(document.getElementById('container'), {
                    center: center,//设置地图中心点坐标

                }); 
                //初始化label
                let label = new TMap.MultiLabel({
                    id: 'label-layer',
                    map: map,
                    styles: {
                            'label': new TMap.LabelStyle({
                            'color': '#1890FF', //颜色属性
                            'size': 29, //文字大小属性
                            'offset': { x: 0, y: 0 }, //文字偏移属性单位为像素
                            'angle': 0, //文字旋转属性
                            'alignment': 'center', //文字水平对齐属性
                            'verticalAlignment': 'middle' //文字垂直对齐属性
                        })
                    },
                    geometries: this.address
                });
                let bounds = new TMap.LatLngBounds();
                //自适应边际
                //判断标注点是否在范围内
                this.address.forEach(function(item){
                    //若坐标点不在范围内，扩大bounds范围
                    if(bounds.isEmpty() || !bounds.contains(item.position)){
                        bounds.extend(item.position);
                    }
                })
                //设置地图可视范围
                map.fitBounds(bounds, {
                    padding: 100 // 自适应边距
                });
                this.map = map
            }, 1000)
        }
    }
}
</script>
<style scoped>

</style>