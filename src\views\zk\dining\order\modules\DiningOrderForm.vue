<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <!-- 主表单区域 -->
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24" >
            <a-form-item label="用户" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['uid', validatorRules.uid]" dict="dining_user,nickname,id"  />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="商户订单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['ordernum', validatorRules.ordernum]" placeholder="请输入商户订单号" ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="微信订单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['wxOrdernum']" placeholder="请输入微信订单号" ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="商品" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['productId']" dict="dining_product,name,id"  />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="套餐" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['foodsId']" dict="dining_foods,name,id"  />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="预约时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择预约时间" v-decorator="['time', validatorRules.time]" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['phone', validatorRules.phone]" placeholder="请输入联系电话" ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="数量" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['num', validatorRules.num]" placeholder="请输入数量" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="单价" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['price', validatorRules.price]" placeholder="请输入单价" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="总价" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['payPrice', validatorRules.payPrice]" placeholder="请输入总价" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['status', validatorRules.status]" :trigger-change="true" dictCode="zk_order" placeholder="请选择状态" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['remark']" placeholder="请输入备注" ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="支付时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择支付时间" v-decorator="['payTime']" :trigger-change="true" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
      <!-- 子表单区域 -->
    <a-tabs v-model="activeKey" @change="handleChangeTabs">
      <a-tab-pane tab="订单附表" :key="refKeys[0]" :forceRender="true">
        <j-editable-table
          :ref="refKeys[0]"
          :loading="diningOrderAttrTable.loading"
          :columns="diningOrderAttrTable.columns"
          :dataSource="diningOrderAttrTable.dataSource"
          :maxHeight="300"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :actionButton="true"/>
      </a-tab-pane>
    </a-tabs>
  </a-spin>
</template>

<script>

  import pick from 'lodash.pick'
  import { getAction } from '@/api/manage'
  import { FormTypes,getRefPromise } from '@/utils/JEditableTableUtil'
  import { JEditableTableMixin } from '@/mixins/JEditableTableMixin'
  import { validateDuplicateValue } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JDate from '@/components/jeecg/JDate'  
  import JDictSelectTag from "@/components/dict/JDictSelectTag"
  import JSearchSelectTag from '@/components/dict/JSearchSelectTag'

  export default {
    name: 'DiningOrderForm',
    mixins: [JEditableTableMixin],
    components: {
      JFormContainer,
      JDate,
      JDictSelectTag,
      JSearchSelectTag,
    },
    data() {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        labelCol2: {
          xs: { span: 24 },
          sm: { span: 3 },
        },
        wrapperCol2: {
          xs: { span: 24 },
          sm: { span: 20 },
        },
        // 新增时子表默认添加几行空数据
        addDefaultRowNum: 1,
        validatorRules: {
          uid: {
            rules: [
              { required: true, message: '请输入用户!'},
            ]
          },
          ordernum: {
            rules: [
              { required: true, message: '请输入商户订单号!'},
            ]
          },
          time: {
            rules: [
              { required: true, message: '请输入预约时间!'},
            ]
          },
          phone: {
            rules: [
              { required: true, message: '请输入联系电话!'},
            ]
          },
          num: {
            rules: [
              { required: true, message: '请输入数量!'},
            ]
          },
          price: {
            rules: [
              { required: true, message: '请输入单价!'},
            ]
          },
          payPrice: {
            rules: [
              { required: true, message: '请输入总价!'},
            ]
          },
          status: {
            initialValue:0,
            rules: [
              { required: true, message: '请输入状态!'},
            ]
          },
        },
        refKeys: ['diningOrderAttr', ],
        tableKeys:['diningOrderAttr', ],
        activeKey: 'diningOrderAttr',
        // 订单附表
        diningOrderAttrTable: {
          loading: false,
          dataSource: [],
          columns: [
            {
              title: '商品',
              key: 'productId',
              type: FormTypes.sel_search,
              dictCode:"dining_product,name,id",
              width:"200px",
              placeholder: '请输入${title}',
              defaultValue:'',
            },
          ]
        },
        url: {
          add: "/order/diningOrder/add",
          edit: "/order/diningOrder/edit",
          queryById: "/order/diningOrder/queryById",
          diningOrderAttr: {
            list: '/order/diningOrder/queryDiningOrderAttrByMainId'
          },
        }
      }
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：false流程表单 true普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    methods: {
      addBefore(){
        this.form.resetFields()
        this.diningOrderAttrTable.dataSource=[]
      },
      getAllTable() {
        let values = this.tableKeys.map(key => getRefPromise(this, key))
        return Promise.all(values)
      },
      /** 调用完edit()方法之后会自动调用此方法 */
      editAfter() {
        let fieldval = pick(this.model,'uid','address','ordernum','wxOrdernum','productId','foodsId','time','phone','num','price','payPrice','status','remark','payTime','createTime')
        this.$nextTick(() => {
          this.form.setFieldsValue(fieldval)
        })
        // 加载子表数据
        if (this.model.id) {
          let params = { id: this.model.id }
          this.requestSubTableData(this.url.diningOrderAttr.list, params, this.diningOrderAttrTable)
        }
      },
      /** 整理成formData */
      classifyIntoFormData(allValues) {
        let main = Object.assign(this.model, allValues.formValue)
        return {
          ...main, // 展开
          diningOrderAttrList: allValues.tablesValue[0].values,
        }
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          })
        }
      },
      validateError(msg){
        this.$message.error(msg)
      },
     popupCallback(row){
       this.form.setFieldsValue(pick(row,'uid','address','ordernum','wxOrdernum','productId','foodsId','time','phone','num','price','payPrice','status','remark','payTime','createTime'))
     },

    }
  }
</script>

<style scoped>
</style>