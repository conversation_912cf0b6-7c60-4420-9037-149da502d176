<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="名称">
              <a-input placeholder="请输入名称" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <!-- <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="房源类型">
              <j-dict-select-tag placeholder="请选择房源类型" v-model="queryParam.isType" dictCode="house_is_type"/>
            </a-form-item>
          </a-col> -->
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="户型">
              <j-dict-select-tag placeholder="请选择户型" v-model="queryParam.roomType" dictCode="house_type"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="状态">
              <j-dict-select-tag placeholder="请选择状态" v-model="queryParam.status" dictCode="zk_status" />
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="街道">
                <j-category-select placeholder="请选择街道" v-model="queryParam.city" pcode="" />
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="租期分类">
                <j-dict-select-tag
                  placeholder="请选择租期分类"
                  v-model="queryParam.category"
                  dictCode="real_category"
                />
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="所在小区">
                <j-search-select-tag
                  placeholder="请选择所在小区"
                  v-model="queryParam.communityId"
                  dict="rent_community,name,id"
                />
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="付款方式">
                <j-dict-select-tag placeholder="请选择付款方式" v-model="queryParam.payType" dictCode="rent_pay_type" />
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('房源管理')">导出</a-button>
      <a-upload
        name="file"
        :showUploadList="false"
        :multiple="false"
        :headers="tokenHeader"
        :action="importExcelUrl"
        @change="handleImportExcel"
      >
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query
        :fieldList="superFieldList"
        ref="superQueryModal"
        @handleSuperQuery="handleSuperQuery"
      ></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down"/></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            height="25px"
            alt=""
            style="max-width:80px;font-size: 12px;font-style: italic;"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>

    <rent-carousel-modal ref="modalForm" @ok="modalFormOk"></rent-carousel-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import RentCarouselModal from './modules/RentCarouselModal'
import { loadCategoryData } from '@/api/api'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'

export default {
  name: 'RentCarouselList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    RentCarouselModal
  },
  data() {
    return {
      description: '房源管理管理页面',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '发布者',
          align: 'center',
          dataIndex: 'createBy'
        },
        {
          title: '发布者手机号',
          align: 'center',
          dataIndex: 'createBy_dictText'
        },
        {
          title: '名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '类型',
          align: 'center',
          dataIndex: 'houseLeaseType_dictText'
        },
        {
          title: '户型',
          align: 'center',
          dataIndex: 'roomType_dictText'
        },
        {
          title: '朝向',
          align: 'center',
          dataIndex: 'housePoint_dictText'
        },
        {
          title: '总楼层',
          align: 'center',
          dataIndex: 'houseFloor2'
        },
        {
          title: '楼层',
          align: 'center',
          dataIndex: 'houseFloor1'
        },
        {
          title: '电梯',
          align: 'center',
          dataIndex: 'houseLift_dictText'
        },
        {
          title: '租期分类',
          align: 'center',
          dataIndex: 'category_dictText'
        },
        {
          title: '面积',
          align: 'center',
          dataIndex: 'area'
        },
        {
          title: '所在小区',
          align: 'center',
          dataIndex: 'communityId_dictText'
        },
        {
          title: '街道',
          align: 'center',
          dataIndex: 'city',
          customRender: text => (text ? filterMultiDictText(this.dictOptions['city'], text) : '')
        },
        {
          title: '轮播图',
          align: 'center',
          dataIndex: 'image',
          scopedSlots: { customRender: 'imgSlot' }
        },
        {
          title: '付款方式',
          align: 'center',
          dataIndex: 'payType_dictText'
        },
        {
          title: '租金',
          align: 'center',
          sorter: true,
          dataIndex: 'price'
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
            title:'房源类型',
            align:"center",
            dataIndex: 'isType_dictText'
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          title: '房主联系电话',
          align: 'center',
          dataIndex: 'adminPhone'
        },
        {
          title: '排序',
          align: 'center',
          sorter: true,
          dataIndex: 'sort'
        },

        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/carousel/rentCarousel/list',
        delete: '/carousel/rentCarousel/delete',
        deleteBatch: '/carousel/rentCarousel/deleteBatch',
        exportXlsUrl: '/carousel/rentCarousel/exportXls',
        importExcelUrl: 'carousel/rentCarousel/importExcel'
      },
      dictOptions: {},
      superFieldList: []
    }
  },
  created() {
    if (this.$store.state.user.info.departIds !== '41e57edcdf604eb0a89b9290881adde5') {
      this.columns = [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '管理员备注',
          align: 'center',
          dataIndex: 'adminRemark'
        },
        {
          title: '发布者',
          align: 'center',
          dataIndex: 'createBy'
        },
        {
          title: '发布者手机号',
          align: 'center',
          dataIndex: 'createBy_dictText'
        },
        {
          title: '名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '类型',
          align: 'center',
          dataIndex: 'houseLeaseType_dictText'
        },
        {
          title: '户型',
          align: 'center',
          dataIndex: 'roomType_dictText'
        },
        {
          title: '朝向',
          align: 'center',
          dataIndex: 'housePoint_dictText'
        },
        {
          title: '总楼层',
          align: 'center',
          dataIndex: 'houseFloor2'
        },
        {
          title: '楼层',
          align: 'center',
          dataIndex: 'houseFloor1'
        },
        {
          title: '电梯',
          align: 'center',
          dataIndex: 'houseLift_dictText'
        },
        {
          title: '租期分类',
          align: 'center',
          dataIndex: 'category_dictText'
        },
        {
          title: '面积',
          align: 'center',
          dataIndex: 'area'
        },
        {
          title: '所在小区',
          align: 'center',
          dataIndex: 'communityId_dictText'
        },
        {
          title: '街道',
          align: 'center',
          dataIndex: 'city',
          customRender: text => (text ? filterMultiDictText(this.dictOptions['city'], text) : '')
        },
        {
          title: '轮播图',
          align: 'center',
          dataIndex: 'image',
          scopedSlots: { customRender: 'imgSlot' }
        },
        {
          title: '付款方式',
          align: 'center',
          dataIndex: 'payType_dictText'
        },
        {
          title: '租金',
          align: 'center',
          sorter: true,
          dataIndex: 'price'
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
            title:'房源类型',
            align:"center",
            dataIndex: 'isType_dictText'
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          title: '房主联系电话',
          align: 'center',
          dataIndex: 'adminPhone'
        },
        {
          title: '排序',
          align: 'center',
          sorter: true,
          dataIndex: 'sort'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ]
      console.log(this.columns)
    }
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    initDictConfig() {
      loadCategoryData({ code: '' }).then(res => {
        if (res.success) {
          this.$set(this.dictOptions, 'city', res.result)
        }
      })
    },
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'createBy', text: '发布者手机号', dictCode: 'sys_user,phone,username' })
      fieldList.push({ type: 'string', value: 'name', text: '名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'houseLeaseType', text: '类型', dictCode: 'house_lease_type' })
      fieldList.push({ type: 'string', value: 'roomType', text: '户型', dictCode: 'house_type' })
      fieldList.push({ type: 'string', value: 'housePoint', text: '朝向', dictCode: 'house_point' })
      fieldList.push({ type: 'string', value: 'houseFloor2', text: '总楼层', dictCode: '' })
      fieldList.push({ type: 'string', value: 'houseFloor1', text: '楼层', dictCode: '' })
      fieldList.push({ type: 'string', value: 'houseLift', text: '电梯', dictCode: 'house_lift' })
      fieldList.push({ type: 'string', value: 'category', text: '租期分类', dictCode: 'real_category' })
      fieldList.push({ type: 'string', value: 'area', text: '面积', dictCode: '' })
      fieldList.push({
        type: 'sel_search',
        value: 'communityId',
        text: '所在小区',
        dictTable: 'rent_community',
        dictText: 'name',
        dictCode: 'id'
      })
      fieldList.push({ type: 'string', value: 'city', text: '街道' })
      fieldList.push({ type: 'Text', value: 'image', text: '轮播图', dictCode: '' })
      fieldList.push({ type: 'string', value: 'phone', text: '联系手机号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'address', text: '详细地址', dictCode: '' })
      fieldList.push({ type: 'string', value: 'tag', text: '标签', dictCode: 'real_tag' })
      fieldList.push({ type: 'string', value: 'description', text: '配置', dictCode: 'rent_icon,name,id' })
      fieldList.push({ type: 'Text', value: 'info', text: '简介', dictCode: '' })
      fieldList.push({ type: 'string', value: 'payType', text: '付款方式', dictCode: 'rent_pay_type' })
      fieldList.push({ type: 'string', value: 'price', text: '租金', dictCode: '' })
      fieldList.push({ type: 'string', value: 'priceV1', text: '押金', dictCode: '' })
      fieldList.push({ type: 'string', value: 'priceV2', text: '服务费', dictCode: '' })
      fieldList.push({ type: 'int', value: 'status', text: '状态', dictCode: 'zk_status' })
      fieldList.push({ type: 'int', value: 'sort', text: '排序', dictCode: '' })
      fieldList.push({ type: 'string', value: 'adminPhone', text: '房主联系电话', dictCode: '' })
      this.superFieldList = fieldList
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
