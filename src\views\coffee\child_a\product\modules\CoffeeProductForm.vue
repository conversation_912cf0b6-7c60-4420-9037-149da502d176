<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <!-- 主表单区域 -->
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24" >
            <a-form-item label="店铺" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['shopId', validatorRules.shopId]" dict="coffee_shop,name,id" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="分类" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['cateId', validatorRules.cateId]" dict="coffee_category,name,id" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="图片" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-image-upload isMultiple v-decorator="['img']"></j-image-upload>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="简介" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['info']" placeholder="请输入简介"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="关键字" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['keyword']" placeholder="请输入关键字"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="销售量" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['sales', validatorRules.sales]" placeholder="请输入销售量" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="排序" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['sort', validatorRules.sort]" placeholder="请输入排序" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="库存" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['stock', validatorRules.stock]" placeholder="请输入库存" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
	          <j-markdown-editor v-decorator="['description']" id="description"></j-markdown-editor>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="是否推荐" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['isGood', validatorRules.isGood]" :trigger-change="true" dictCode="is_good" placeholder="请选择是否推荐"/>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['isStatus', validatorRules.isStatus]" :trigger-change="true" dictCode="zk_status" placeholder="请选择状态"/>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
      <!-- 子表单区域 -->
    <a-tabs v-model="activeKey" @change="handleChangeTabs">
      <a-tab-pane tab="商品属性表" :key="refKeys[0]" :forceRender="true">
        <j-editable-table
          :ref="refKeys[0]"
          :loading="coffeeAttrTable.loading"
          :columns="coffeeAttrTable.columns"
          :dataSource="coffeeAttrTable.dataSource"
          :maxHeight="300"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :actionButton="true"/>
      </a-tab-pane>
    </a-tabs>
  </a-spin>
</template>

<script>

  import pick from 'lodash.pick'
  import { getAction } from '@/api/manage'
  import { FormTypes,getRefPromise } from '@/utils/JEditableTableUtil'
  import { JEditableTableMixin } from '@/mixins/JEditableTableMixin'
  import { validateDuplicateValue } from '@/utils/util'
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JImageUpload from '@/components/jeecg/JImageUpload'
  import JDictSelectTag from "@/components/dict/JDictSelectTag"
  import JSearchSelectTag from '@/components/dict/JSearchSelectTag'
  import JMarkdownEditor from '@/components/jeecg/JMarkdownEditor/index'

  export default {
    name: 'CoffeeProductForm',
    mixins: [JEditableTableMixin],
    components: {
      JFormContainer,
      JImageUpload,
      JDictSelectTag,
      JSearchSelectTag,
      JMarkdownEditor,
    },
    data() {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        labelCol2: {
          xs: { span: 24 },
          sm: { span: 3 },
        },
        wrapperCol2: {
          xs: { span: 24 },
          sm: { span: 20 },
        },
        // 新增时子表默认添加几行空数据
        addDefaultRowNum: 1,
        validatorRules: {
          shopId: {
            rules: [
              { required: true, message: '请输入店铺!'},
            ]
          },
          cateId: {
            rules: [
              { required: true, message: '请输入分类!'},
            ]
          },
          name: {
            rules: [
              { required: true, message: '请输入名称!'},
            ]
          },
          sales: {
            rules: [
              { required: true, message: '请输入销售量!'},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
            ]
          },
          sort: {
            rules: [
              { required: true, message: '请输入排序!'},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
            ]
          },
          stock: {
            rules: [
              { required: true, message: '请输入库存!'},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
            ]
          },
          isGood: {
            rules: [
              { required: true, message: '请输入是否推荐!'},
            ]
          },
          isStatus: {
            rules: [
              { required: true, message: '请输入状态!'},
            ]
          },
        },
        refKeys: ['coffeeAttr', ],
        tableKeys:['coffeeAttr', ],
        activeKey: 'coffeeAttr',
        // 商品属性表
        coffeeAttrTable: {
          loading: false,
          dataSource: [],
          columns: [
            {
              title: '属性',
              key: 'name',
              type: FormTypes.select,
              dictCode:"coffee_attr",
              width:"200px",
              placeholder: '请输入${title}',
              defaultValue: '',
              validateRules: [{ required: true, message: '${title}不能为空' }],
            },
            {
              title: '值',
              key: 'value',
              type: FormTypes.input,
              width:"200px",
              placeholder: '请输入${title}',
              defaultValue: '',
              validateRules: [{ required: true, message: '${title}不能为空' }],
            },
            {
              title: '价格',
              key: 'price',
              type: FormTypes.inputNumber,
              width:"200px",
              placeholder: '请输入${title}',
              defaultValue: '',
              validateRules: [{ required: true, message: '${title}不能为空' }],
            },
            {
              title: '状态',
              key: 'isStatus',
              type: FormTypes.select,
              dictCode:"zk_status",
              width:"200px",
              placeholder: '请输入${title}',
              defaultValue: '',
              validateRules: [{ required: true, message: '${title}不能为空' }],
            },
          ]
        },
        url: {
          add: "/product/coffeeProduct/add",
          edit: "/product/coffeeProduct/edit",
          queryById: "/product/coffeeProduct/queryById",
          coffeeAttr: {
            list: '/product/coffeeProduct/queryCoffeeAttrByMainId'
          },
        }
      }
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：false流程表单 true普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    methods: {
      addBefore(){
        this.form.resetFields()
        this.coffeeAttrTable.dataSource=[]
      },
      getAllTable() {
        let values = this.tableKeys.map(key => getRefPromise(this, key))
        return Promise.all(values)
      },
      /** 调用完edit()方法之后会自动调用此方法 */
      editAfter() {
        let fieldval = pick(this.model,'shopId','cateId','name','img','info','keyword','sales','sort','stock','description','isGood','isStatus')
        this.$nextTick(() => {
          this.form.setFieldsValue(fieldval)
        })
        // 加载子表数据
        if (this.model.id) {
          let params = { id: this.model.id }
          this.requestSubTableData(this.url.coffeeAttr.list, params, this.coffeeAttrTable)
        }
      },
      /** 整理成formData */
      classifyIntoFormData(allValues) {
        let main = Object.assign(this.model, allValues.formValue)
        return {
          ...main, // 展开
          coffeeAttrList: allValues.tablesValue[0].values,
        }
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          })
        }
      },
      validateError(msg){
        this.$message.error(msg)
      },
     popupCallback(row){
       this.form.setFieldsValue(pick(row,'shopId','cateId','name','img','info','keyword','sales','sort','stock','description','isGood','isStatus'))
     },

    }
  }
</script>

<style scoped>
</style>