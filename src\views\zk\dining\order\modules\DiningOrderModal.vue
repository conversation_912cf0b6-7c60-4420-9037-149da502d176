<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row>
          <a-col :span="24">
            <a-form-item label="用户" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['uid', validatorRules.uid]" dict="dining_user,truename,id"  />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="商户订单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['ordernum', validatorRules.ordernum]" placeholder="请输入商户订单号" ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="微信订单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['wxOrdernum']" placeholder="请输入微信订单号" ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="商品" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['productId']" dict="dining_product,name,id"  />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="套餐" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['foodsId']" dict="dining_foods,name,id"  />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="预约时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择预约时间" v-decorator="['time', validatorRules.time]" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['phone', validatorRules.phone]" placeholder="请输入联系电话" ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="数量" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['num', validatorRules.num]" placeholder="请输入数量" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="单价" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['price', validatorRules.price]" placeholder="请输入单价" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="总价" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['payPrice', validatorRules.payPrice]" placeholder="请输入总价" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['status', validatorRules.status]" :trigger-change="true" dictCode="zk_order" placeholder="请选择状态" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['remark']" placeholder="请输入备注" ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="支付时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择支付时间" v-decorator="['payTime']" :trigger-change="true" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>

  import { httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: "DiningOrderModal",
    components: { 
    },
    data () {
      return {
        form: this.$form.createForm(this),
        title:"操作",
        width:800,
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },

        confirmLoading: false,
        validatorRules: {
          uid: {
            rules: [
              { required: true, message: '请输入用户!'},
            ]
          },
          ordernum: {
            rules: [
              { required: true, message: '请输入商户订单号!'},
            ]
          },
          time: {
            rules: [
              { required: true, message: '请输入预约时间!'},
            ]
          },
          phone: {
            rules: [
              { required: true, message: '请输入联系电话!'},
            ]
          },
          num: {
            rules: [
              { required: true, message: '请输入数量!'},
            ]
          },
          price: {
            rules: [
              { required: true, message: '请输入单价!'},
            ]
          },
          payPrice: {
            rules: [
              { required: true, message: '请输入总价!'},
            ]
          },
          status: {
            initialValue:0,
            rules: [
              { required: true, message: '请输入状态!'},
            ]
          },
        },
        url: {
          add: "/order/diningOrder/add",
          edit: "/order/diningOrder/edit",
        }
     
      }
    },
    created () {
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'uid','address','ordernum','wxOrdernum','productId','foodsId','time','phone','num','price','payPrice','status','remark','payTime','createTime'))
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            let formData = Object.assign(this.model, values);
            console.log("表单提交数据",formData)
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }
         
        })
      },
      handleCancel () {
        this.close()
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'uid','address','ordernum','wxOrdernum','productId','foodsId','time','phone','num','price','payPrice','status','remark','payTime','createTime'))
      },

      
    }
  }
</script>