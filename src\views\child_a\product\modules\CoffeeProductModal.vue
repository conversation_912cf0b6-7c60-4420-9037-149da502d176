<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row>
          <a-col :span="24">
            <a-form-item label="店铺" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['shopId', validatorRules.shopId]" dict="coffee_shop,name,id" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="分类" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['cateId', validatorRules.cateId]" dict="coffee_category,name,id" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="图片" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-image-upload isMultiple v-decorator="['img']"></j-image-upload>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="简介" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['info']" placeholder="请输入简介"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="关键字" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['keyword']" placeholder="请输入关键字"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="销售量" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['sales', validatorRules.sales]" placeholder="请输入销售量" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="排序" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['sort', validatorRules.sort]" placeholder="请输入排序" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="库存" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['stock', validatorRules.stock]" placeholder="请输入库存" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
	          <j-markdown-editor v-decorator="['description']" id="description"></j-markdown-editor>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="是否推荐" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['isGood', validatorRules.isGood]" :trigger-change="true" dictCode="is_good" placeholder="请选择是否推荐"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['isStatus', validatorRules.isStatus]" :trigger-change="true" dictCode="zk_status" placeholder="请选择状态"/>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>

  import { httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'
  import JImageUpload from '@/components/jeecg/JImageUpload'
  import JDictSelectTag from "@/components/dict/JDictSelectTag"
  import JSearchSelectTag from '@/components/dict/JSearchSelectTag'
  import JMarkdownEditor from '@/components/jeecg/JMarkdownEditor/index'

  export default {
    name: "CoffeeProductModal",
    components: { 
      JImageUpload,
      JDictSelectTag,
      JSearchSelectTag,
      JMarkdownEditor,
    },
    data () {
      return {
        form: this.$form.createForm(this),
        title:"操作",
        width:800,
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },

        confirmLoading: false,
        validatorRules: {
          shopId: {
            rules: [
              { required: true, message: '请输入店铺!'},
            ]
          },
          cateId: {
            rules: [
              { required: true, message: '请输入分类!'},
            ]
          },
          name: {
            rules: [
              { required: true, message: '请输入名称!'},
            ]
          },
          sales: {
            rules: [
              { required: true, message: '请输入销售量!'},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
            ]
          },
          sort: {
            rules: [
              { required: true, message: '请输入排序!'},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
            ]
          },
          stock: {
            rules: [
              { required: true, message: '请输入库存!'},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
            ]
          },
          isGood: {
            rules: [
              { required: true, message: '请输入是否推荐!'},
            ]
          },
          isStatus: {
            rules: [
              { required: true, message: '请输入状态!'},
            ]
          },
        },
        url: {
          add: "/product/coffeeProduct/add",
          edit: "/product/coffeeProduct/edit",
        }
     
      }
    },
    created () {
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'shopId','cateId','name','img','info','keyword','sales','sort','stock','description','isGood','isStatus'))
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            let formData = Object.assign(this.model, values);
            console.log("表单提交数据",formData)
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.close();
            })
          }
         
        })
      },
      handleCancel () {
        this.close()
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'shopId','cateId','name','img','info','keyword','sales','sort','stock','description','isGood','isStatus'))
      },

      
    }
  }
</script>