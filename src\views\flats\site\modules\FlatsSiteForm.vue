<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <!-- 页面载入后，调用init函数 -->
          <div id="container" v-bind:class="{ active: isActive, noshow:isShow}"></div>
          <a-col :span="24">
            <a-form-model-item label="站点名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
              <a-input v-model="model.name" placeholder="请输入站点名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="站点类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="category">
              <j-dict-select-tag type="radio" v-model="model.category" dictCode="flats_category" placeholder="请选择站点类型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="站点地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address" @>
               <a-input v-model="model.address" placeholder="请输入地址" disabled  style="width: 200px;"></a-input>
               <a-button type="primary" icon="arrow-down"  v-on:click="admap()" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="管理员" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="manager">
              <j-search-select-tag v-model="model.manager" dict="flats_user,name,id"  />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="phone">
              <a-input v-model="model.phone" placeholder="请输入联系电话"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="简介" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="description">
              <j-editor v-model="model.description" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isStatus">
              <j-dict-select-tag type="radio" v-model="model.isStatus" dictCode="zk_status" placeholder="请选择状态" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'FlatsSiteForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        isShow:false,
        isActive:false,
        isDeduplication_map:false,
        model:{
            category:"0",
            isStatus:"0",
            address:'',
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           phone: [
              { required: false},
              { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码!'},
           ],
           isStatus: [
              { required: true, message: '请输入状态!'},
           ],
        },
        url: {
          add: "/site/flatsSite/add",
          edit: "/site/flatsSite/edit",
          queryById: "/site/flatsSite/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
      
      let script = document.createElement("script");
      //设置标签的type属性
      script.type = "text/javascript";
      //设置标签的链接地址
      script.src = "https://map.qq.com/api/gljs?v=1.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77";
      //添加标签到dom
      document.body.appendChild(script);
    },
    methods: {
      admap(){
        var that = this;
        if(!that.isActive) {
          //样式显示
          that.isShow = false;
          that.isActive = true;
          if(!that.isDeduplication_map) {
            //定义地图中心点坐标
            var center = new TMap.LatLng(39.984120, 116.307484)
            //定义map变量，调用 TMap.Map() 构造函数创建地图
            var map = new TMap.Map(document.getElementById('container'), {
                center: center,//设置地图中心点坐标
            }); 
            //坐标设置
            //初始化marker图层
            var markerLayer = new TMap.MultiMarker({
                id: 'marker-layer',
                map: map
            });
            //定义坐标拾取器
            var clickHandler=function(evt){
                var lat = evt.latLng.getLat().toFixed(7);
                var lng = evt.latLng.getLng().toFixed(7);
                that.model.address = lat + "," + lng;
                that.model.lat = lat;
                that.model.lng = lng;
                markerLayer.setGeometries([])
                markerLayer.add({
                  position: evt.latLng
                });   
                console.log("您点击的的坐标是："+ lat + "," + lng);
            }
            //Map实例创建后，通过on方法绑定点击事件
            map.on("click",clickHandler)
            that.isDeduplication_map = true;
          }
        } else {
          that.isShow = true;
          that.isActive = false;
        }
      },
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>