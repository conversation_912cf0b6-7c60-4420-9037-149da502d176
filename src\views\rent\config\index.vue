<template>
  <a-card :bordered="false">
    <a-form-model
      ref="elForm"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 3, offset: 0 }"
      :wrapper-col="{ span: 21, offset: 0 }"
      layout="horizontal"
    >
      <a-form-model-item label="客服手机号-工作时间" prop="phone">
        <a-input v-model="formData.phone" placeholder="请输入手机号" :maxLength="20" allow-clear>
          <template slot="addonBefore">
            <a-icon type="phone" />
          </template>
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="客服手机号-非工作时间" prop="phone2">
        <a-input v-model="formData.phone2" placeholder="请输入手机号" :maxLength="20" allow-clear>
          <template slot="addonBefore">
            <a-icon type="phone" />
          </template>
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="微信小程序appId">
        <a-input v-model="formData.app_id" placeholder="appId" allow-clear>
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="微信小程序secret">
        <a-input v-model="formData.secret" placeholder="secret" allow-clear>
        </a-input>
      </a-form-model-item>
      <a-form-model-item label="是否开启首页弹窗" prop="reply_verify">
        <a-radio-group v-model="formData.reply_verify" size="large">
          <a-radio
            v-for="(item, index) in reply_verifyOptions"
            :key="index"
            :value="item.value"
            :disabled="item.disabled"
            >{{ item.label }}</a-radio
          >
        </a-radio-group>
      </a-form-model-item>
      <a-col :span="24">
          <a-form-model-item label="首页弹窗图">
            <j-image-upload  v-model="formData.image" ></j-image-upload>
          </a-form-model-item>
      </a-col>
      <a-col :span="24">
          <a-form-model-item label="登陆图标">
            <j-image-upload  v-model="formData.src" ></j-image-upload>
          </a-form-model-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="关于我们">
          <j-editor v-model="formData.content" />
        </a-form-item>
      </a-col>
      <a-button type="primary" @click="submitForm" block :loading="loading">提交</a-button>
    </a-form-model>
  </a-card>
</template>

<script>
import JEditor from '@/components/jeecg/JEditor'
import { httpAction } from '@/api/manage'
export default {
  components: {
    JEditor
  },
  props: [],
  data() {
    return {
      loading: false,
      formData: {
        phone: undefined,
        phone2: undefined,
        reply_verify: undefined,
        content:'',
        src:'',
        app_id:'',
        secret:'',
      },
      reply_verifyOptions: [
        {
          label: '开启',
          value: 2
        },
        {
          label: '关闭',
          value: 1
        }
      ],
      rules: {
        phone: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur'
          }
        ],
        phone2: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {},
  watch: {},
  created() {
      httpAction('https://api.rent.zkshlm.com/common/rent/getConfig',this.formData,'post').then((res)=>{
        this.formData = res.data
      }).finally(() => {

      })
  },
  mounted() {

  },
  methods: {
    submitForm() {
      const that = this;
      this.$refs['elForm'].validate(valid => {
        if (valid) {
          that.loading = true
          httpAction('https://api.rent.zkshlm.com/common/rent/setconfig',this.formData,'post').then((res)=>{
            if(res.code == '200'){
              that.formData.reply_verify  = res.reply_verify;
              that.$message.success(res.msg);
            }else{
              that.$message.warning(res.msg);
            }
            that.loading = false;
          }).finally(() => {
            that.loading = false;
          })
        }
      })
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
