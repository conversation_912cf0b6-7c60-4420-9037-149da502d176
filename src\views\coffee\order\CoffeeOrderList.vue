<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="店铺">
              <j-search-select-tag placeholder="请选择店铺" v-model="queryParam.shopId" dict="coffee_shop,name,id"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="订单类型">
              <j-dict-select-tag placeholder="请选择订单类型" v-model="queryParam.type" dictCode="order_type"/>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="门店收银台下单">
                <j-dict-select-tag placeholder="请选择门店收银台下单" v-model="queryParam.substitute" dictCode="coffee_substitute"/>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="用户">
                <j-search-select-tag placeholder="请选择用户" v-model="queryParam.userId" dict="coffee_user,nickname,id"/>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="商品">
                <j-search-select-tag placeholder="请选择商品" v-model="queryParam.productId" dict="coffee_product,name,id"/>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="取餐号">
                <a-input placeholder="请输入取餐号" v-model="queryParam.takeNum"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="联系电话">
                <a-input placeholder="请输入联系电话" v-model="queryParam.phone"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="状态">
                <j-dict-select-tag placeholder="请选择状态" v-model="queryParam.status" dictCode="coffee_status"/>
              </a-form-item>
            </a-col>
            <a-col :xl="10" :lg="11" :md="12" :sm="24">
              <a-form-item label="核销时间">
                <j-date :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择开始时间" class="query-group-cust" v-model="queryParam.payTime_begin"></j-date>
                <span class="query-group-split-cust"></span>
                <j-date :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择结束时间" class="query-group-cust" v-model="queryParam.payTime_end"></j-date>
              </a-form-item>
            </a-col>
            <a-col :xl="10" :lg="11" :md="12" :sm="24">
              <a-form-item label="创建时间">
                <j-date :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择开始时间" class="query-group-cust" v-model="queryParam.createTime_begin"></j-date>
                <span class="query-group-split-cust"></span>
                <j-date :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择结束时间" class="query-group-cust" v-model="queryParam.createTime_end"></j-date>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="核销员">
                <j-search-select-tag placeholder="请选择核销员" v-model="queryParam.verifyServiceId" dict="coffee_user,nickname,id"/>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="用户删除订单标记">
                <j-dict-select-tag placeholder="请选择用户删除订单标记" v-model="queryParam.isDel" dictCode="zk_del"/>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('订单管理')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <coffee-order-modal ref="modalForm" @ok="modalFormOk"></coffee-order-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import CoffeeOrderModal from './modules/CoffeeOrderModal'
  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'

  export default {
    name: 'CoffeeOrderList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      CoffeeOrderModal
    },
    data () {
      return {
        description: '订单管理管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'店铺',
            align:"center",
            dataIndex: 'shopId_dictText'
          },
          {
            title:'订单类型',
            align:"center",
            dataIndex: 'type_dictText'
          },
          {
            title:'门店收银台下单',
            align:"center",
            dataIndex: 'substitute_dictText'
          },
          {
            title:'预约',
            align:"center",
            dataIndex: 'reserveTime'
          },
          {
            title:'商品',
            align:"center",
            dataIndex: 'productId_dictText'
          },
          {
            title:'商户订单号',
            align:"center",
            dataIndex: 'ordernum'
          },
          {
            title:'取餐号',
            align:"center",
            sorter: true,
            dataIndex: 'takeNum'
          },
          {
            title:'商品属性',
            align:"center",
            dataIndex: 'productAttr'
          },
          {
            title:'商品总数',
            align:"center",
            sorter: true,
            dataIndex: 'num'
          },
          {
            title:'商品单价',
            align:"center",
            sorter: true,
            dataIndex: 'price'
          },
          {
            title:'商品总价',
            align:"center",
            sorter: true,
            dataIndex: 'payPrice'
          },
          {
            title:'联系电话',
            align:"center",
            dataIndex: 'phone'
          },
          {
            title:'状态',
            align:"center",
            sorter: true,
            dataIndex: 'status_dictText'
          },
          {
            title:'预约时间',
            align:"center",
            sorter: true,
            dataIndex: 'reserve'
          },
          {
            title:'核销时间',
            align:"center",
            sorter: true,
            dataIndex: 'payTime'
          },
          {
            title:'创建时间',
            align:"center",
            sorter: true,
            dataIndex: 'createTime'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/order/coffeeOrder/list",
          delete: "/order/coffeeOrder/delete",
          deleteBatch: "/order/coffeeOrder/deleteBatch",
          exportXlsUrl: "/order/coffeeOrder/exportXls",
          importExcelUrl: "order/coffeeOrder/importExcel",
          
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'sel_search',value:'shopId',text:'店铺',dictTable:'coffee_shop', dictText:'name', dictCode:'id'})
        fieldList.push({type:'string',value:'type',text:'订单类型',dictCode:'order_type'})
        fieldList.push({type:'int',value:'substitute',text:'门店收银台下单',dictCode:'coffee_substitute'})
        fieldList.push({type:'string',value:'reserveTime',text:'预约',dictCode:''})
        fieldList.push({type:'sel_search',value:'userId',text:'用户',dictTable:'coffee_user', dictText:'nickname', dictCode:'id'})
        fieldList.push({type:'sel_search',value:'productId',text:'商品',dictTable:'coffee_product', dictText:'name', dictCode:'id'})
        fieldList.push({type:'string',value:'ordernum',text:'商户订单号',dictCode:''})
        fieldList.push({type:'string',value:'takeNum',text:'取餐号',dictCode:''})
        fieldList.push({type:'string',value:'productAttr',text:'商品属性',dictCode:''})
        fieldList.push({type:'int',value:'num',text:'商品总数',dictCode:''})
        fieldList.push({type:'BigDecimal',value:'price',text:'商品单价',dictCode:''})
        fieldList.push({type:'BigDecimal',value:'payPrice',text:'商品总价',dictCode:''})
        fieldList.push({type:'string',value:'phone',text:'联系电话',dictCode:''})
        fieldList.push({type:'int',value:'status',text:'状态',dictCode:'coffee_status'})
        fieldList.push({type:'string',value:'remark',text:'备注',dictCode:''})
        fieldList.push({type:'datetime',value:'reserve',text:'预约时间'})
        fieldList.push({type:'int',value:'temp',text:'是否团购',dictCode:'coffee_temp'})
        fieldList.push({type:'datetime',value:'payTime',text:'核销时间'})
        fieldList.push({type:'datetime',value:'createTime',text:'创建时间'})
        fieldList.push({type:'sel_search',value:'verifyServiceId',text:'核销员',dictTable:'coffee_user', dictText:'nickname', dictCode:'id'})
        fieldList.push({type:'int',value:'isDel',text:'用户删除订单标记',dictCode:'zk_del'})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>