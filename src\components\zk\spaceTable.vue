<template>
  <div>
    <div>
      <a-card>
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :xl="6" :lg="7" :md="8" :sm="24">
                <a-form-item label="站点">
                  <j-search-select-tag placeholder="请选择站点" v-model="queryParam.siteId" dict="flats_site,name,id" />
                </a-form-item>
              </a-col>
              <a-col :xl="6" :lg="7" :md="8" :sm="24">
                <a-form-item label="性别">
                  <j-dict-select-tag placeholder="请选择性别" v-model="queryParam.sex" dictCode="flats_sex" />
                </a-form-item>
              </a-col>
              <template v-if="toggleSearchStatus">
                <a-col :xl="6" :lg="7" :md="8" :sm="24">
                  <a-form-item label="状态">
                    <j-dict-select-tag placeholder="请选择状态" v-model="queryParam.status" dictCode="zk_status" />
                  </a-form-item>
                </a-col>
                <a-col :xl="6" :lg="7" :md="8" :sm="24">
                  <a-form-item label="已入住数">
                    <a-input placeholder="请输入已入住数" v-model="queryParam.checkIn"></a-input>
                  </a-form-item>
                </a-col>
              </template>
              <a-col :xl="6" :lg="7" :md="8" :sm="24">
                <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                  <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
                  <a @click="handleToggleSearch" style="margin-left: 8px">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- 查询区域-END -->
      </a-card>
    </div>
    <a-spin tip="Loading..." :spinning="loading">
      <a-card title="房间可视化" style="text-align: center">
        <a-card-grid v-for="(item,index) in dataSource" :key="index" :style="getClass(item.num - item.checkIn)">
          <div style="display: grid; grid-template-columns: 3fr 1fr">
            <div>{{item.name}}</div>
            <div>

              <svg v-if="item.sex == 1" t="1623813491748" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="1276" width="50" height="50">
                <path
                  d="M389.551692 624.101147m-31.518854 0a30.801 30.801 0 1 0 63.037708 0 30.801 30.801 0 1 0-63.037708 0ZM652.540362 624.101147m-31.518854 0a30.801 30.801 0 1 0 63.037708 0 30.801 30.801 0 1 0-63.037708 0ZM847.684854 529.096377c-0.045025-4.295839-0.149403-8.621355-0.363274-12.992919 0.13303-2.305509 0.268106-4.625344 0.407276-6.967692 0.378623-6.356778-0.678452-13.248745-2.848884-20.491707C830.993706 380.954385 764.669138 262.137279 610.478384 231.385904c1.904373-6.335289 2.939959-12.993942 2.939959-19.87363 0-41.821501-37.56864-75.724659-83.911108-75.724659s-83.911108 33.903158-83.911108 75.724659c0 6.943133 1.056052 13.660114 2.995217 20.047592-172.023912 35.075867-236.311077 182.030823-237.282195 298.369481-23.9996 17.796318-39.005362 46.121434-39.005362 77.406974 0 39.932478 24.435529 75.05644 60.84067 89.587388 34.624589 219.769332 49.247634 160.39199 49.247634 134.317125 0-11.918447-5.146207-45.87584-6.573719-87.158059 13.103436 24.085558 29.740348 46.427402 49.67026 66.358337 54.438866 54.43989 126.81936 84.420715 203.807802 84.420715s149.368936-29.980825 203.807802-84.420715c12.723789-12.723789 24.091698-26.437116 34.072003-40.950667-1.344624 36.572964-2.363837 68.071352-2.363837 79.698157-0.001023 26.203802 25.240871-6.881734 51.846832-118.42211 2.76395-11.589966 5.240351-22.466688 7.479345-32.766264 37.966707-13.806447 63.677275-49.704029 63.677275-90.664929C887.815853 575.563688 872.344486 546.834366 847.684854 529.096377zM185.606767 607.336322c0-23.657816 9.965979-45.358047 26.38902-60.692291 0.218988 1.860371 0.481977 3.584642 0.799202 5.143137l-0.00614 0.010233c6.465249 50.726312 12.4301 93.470835 17.918092 129.407302C203.428667 667.129126 185.606767 638.983089 185.606767 607.336322zM768.189312 742.575446c-47.387263 82.9492-136.716778 138.982378-238.892879 138.982378-114.103758 0-212.184587-69.87851-253.723655-169.08293 1.255597-58.504462 12.951987-123.56627 55.943127-160.625304 46.228881-39.848567 68.721151-105.32379 79.591733-155.708318 10.060123 44.267203 32.988322 97.670483 85.256756 109.897969 88.686878 20.74651 127.991046 21.788236 168.504762 22.452361 19.190061 0.315178 100.624768-3.069919 108.812241 78.843696C773.682419 607.336322 770.696412 677.210739 768.189312 742.575446zM827.486836 682.174797c11.06501-53.956889 15.004739-90.705861 17.404392-125.483946 1.016143-2.846838 1.76418-6.480598 2.237971-10.962679 17.016559 15.377222 27.384697 37.474496 27.384697 61.60815C874.512872 639.723963 855.849814 668.449191 827.486836 682.174797z"
                  p-id="1277"></path>
              </svg>
              <svg v-else t="1623813491748" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="1276" width="50" height="50">
                <path
                  d="M358.032 624.101a30.801 30.801 0 1 0 63.039 0 30.801 30.801 0 1 0-63.038 0z m262.99 0a30.801 30.801 0 1 0 63.037 0 30.801 30.801 0 1 0-63.037 0z m281.821-218.718s-9.282-79.594-51.964-125.765c0 0 30.007-16.164-2.303-43.842 0 0-28.825-6.924-34.614-4.603 0 0-14.973-50.786-44.979-63.477 0 0-25.401-6.16-31.188 18.465 0 0-67.462-34.322-116.248-42.988 0 0-124.576-23.941-158.028-81.908 0 0-7.225-14.703-30.847-6.064 0 0-22.54 17.877-15.63 89.413 0 0 1.926 14.629-94.235-46.91 0 0-23.83-6.15-30.738 12.298 0 0-12.277 43.84 3.07 76.158 0 0-55.372 9.988-79.258 33.846 0 0-26.88 34.621 18.46 40.003 0 0-67.635 38.459-79.22 146.909 0 0-1.353 76.092 23.042 160.503a96.063 96.063 0 0 0-5.623 44.326c4.764 44.065 38.812 79.014 82.893 85.201 13.907 42.315 37.644 81.083 70.055 113.493 54.438 54.44 126.819 84.42 203.807 84.42s149.37-29.98 203.808-84.42c33.104-33.104 57.143-72.847 70.917-116.22 42.516-5.446 76.437-37.905 83.273-80.361 3.315-20.598-0.159-40.963-9.03-58.588 15.168-44.548 28.314-99.574 24.58-149.89zM249.33 682.28c-34.119-8.148-59.748-36.671-63.563-71.965a83.181 83.181 0 0 1 0.088-18.627c6.146 17.761 13.512 35.604 22.355 52.917 0 0 18.251 18.252 37.067 11.517a287.165 287.165 0 0 0 2.71 13.735l1.343 12.423z m279.966 199.28c-130.877 0-240.674-91.933-268.243-214.609l-2.244-20.76c2.158-2.56 4.264-5.613 6.286-9.26 0 0 2.377-85.405 53.146-136.172 0 0 44.606 12.345 101.533-19.235 0 0-23.867 29.238-45.412 44.62 0 0-12.319 30.005 15.386 33.094 0 0 85.379 16.157 170.757-40.003 0 0-4.605 28.464 36.185 19.984 0 0 33.02-0.757 86.127-41.538 0 0 11.55 19.23 54.978-4.597 0 0-1.124 38.055 33.49 96.9 0 0 15.01 24.478 15.01 67.614 0 0 3.941 3.251 9.851 6.1l-3.555 22.083C758.52 798.913 653.387 881.559 529.296 881.559zM874.16 611.745c-5.58 34.649-32.11 61.567-66.044 68.327a288.21 288.21 0 0 0 2.913-12.22c10.411 0.582 21.853-4.293 29.481-23.616 0 0 15.437-28.599 31.024-70.149a82.776 82.776 0 0 1 2.626 37.658z"
                  p-id="2078"></path>

              </svg>


            </div>
            <div>{{item.checkIn}}/{{item.num}}</div>
            <div></div>
          </div>
        </a-card-grid>
      </a-card>
    </a-spin>
    <div style="text-align: center">
      <a-pagination show-quick-jumper :default-current="ipagination.current" :total="ipagination.total"
        @change="onChange" />
    </div>
  </div>
</template>
<script>
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import {
    getAction
  } from '@/api/manage'
  export default {
    name: 'spaceTable',
    mixins: [JeecgListMixin],
    props: {},
    data() {
      return {
        queryParam: [],
        url: {
          list: "/room/flatsRoom/list",
          delete: "/room/flatsRoom/delete",
          deleteBatch: "/room/flatsRoom/deleteBatch",
          exportXlsUrl: "/room/flatsRoom/exportXls",
          importExcelUrl: "room/flatsRoom/importExcel",
        },
        // 表头
        columns: [

        ],
        dataSource: [],
        selectedMainId: '',
        superFieldList: [],
        loading: false,
        /* 分页参数 */
        ipagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '50'],
          showTotal: (total, range) => {
            return range[0] + "-" + range[1] + " 共" + total + "条"
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
      }
    },
    created() {

    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error("请设置url.list属性!")
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      this.onClearSelected()
      var params = this.getQueryParams(); //查询条件
      this.loading = true;
      params.columns = 'sort';
      params.order = 'asc';
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records;
          this.ipagination.total = res.result.total;
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },
    methods: {
      getClass(x) {
        if (x == 0) {
          return 'background-color: red;width:16%;text-align:center;margin-left:2%;margin-right:2%;margin-top:0.5%;margin-bottom:0.5%;color: rgba(0, 0, 0, 0.85);font-size: 24px;'
        } else if (x == 1) {
          return 'width:16%;text-align:center;margin-left:2%;margin-right:2%;margin-top:0.5%;margin-bottom:0.5%;color: rgba(0, 0, 0, 0.85);font-size: 24px;'
        } else if (x == 2) {
          return 'width:16%;text-align:center;margin-left:2%;margin-right:2%;margin-top:0.5%;margin-bottom:0.5%;color: rgba(0, 0, 0, 0.85);font-size: 24px;'
        } else if (x == 3) {
          return 'width:16%;text-align:center;margin-left:2%;margin-right:2%;margin-top:0.5%;margin-bottom:0.5%;color: rgba(0, 0, 0, 0.85);font-size: 24px;'
        } else {
          return 'width:16%;text-align:center;margin-left:2%;margin-right:2%;margin-top:0.5%;margin-bottom:0.5%;color: rgba(0, 0, 0, 0.85);font-size: 24px;'
        }
      },
      onClearSelected() {
        this.selectedRowKeys = [];
        this.selectionRows = [];
        this.selectedMainId = ''
      },
      onChange(pageNumber) {
        this.ipagination.current = pageNumber
        var params = this.getQueryParams(); //查询条件
        params.column = 'sort';
        params.order = 'asc';
        this.loading = true;
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            this.dataSource = res.result.records;
            this.ipagination.total = res.result.total;
          }
          if (res.code === 510) {
            this.$message.warning(res.message)
          }
          this.loading = false;
        })
      },
    },
  }
</script>
<style scoped>
</style>
