<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="店铺">
              <j-search-select-tag placeholder="请选择店铺" v-model="queryParam.shopId" dict="coffee_shop,name,id"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="分类">
              <j-search-select-tag placeholder="请选择分类" v-model="queryParam.cateId" dict="coffee_category,name,id"/>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="名称">
                <a-input placeholder="请输入名称" v-model="queryParam.name"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="是否推荐">
                <j-dict-select-tag placeholder="请选择是否推荐" v-model="queryParam.isGood" dictCode="is_good"/>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="状态">
                <j-dict-select-tag placeholder="请选择状态" v-model="queryParam.isStatus" dictCode="zk_status"/>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->
    
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('商品管理')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        class="j-table-force-nowrap"
        :scroll="{x:true}"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type:'radio'}"
        :customRow="clickThenSelect"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <a-tabs defaultActiveKey="1">
      <a-tab-pane tab="商品属性表" key="1" >
        <CoffeeAttrList :mainId="selectedMainId" />
      </a-tab-pane>
    </a-tabs>

    <coffeeProduct-modal ref="modalForm" @ok="modalFormOk"></coffeeProduct-modal>
  </a-card>
</template>

<script>

  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import CoffeeProductModal from './modules/CoffeeProductModal'
  import { getAction } from '@/api/manage'
  import CoffeeAttrList from './CoffeeAttrList'
  import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
  import {initDictOptions,filterMultiDictText} from '@/components/dict/JDictSelectUtil'
  import JSearchSelectTag from '@/components/dict/JSearchSelectTag'
  import '@/assets/less/TableExpand.less'

  export default {
    name: "CoffeeProductList",
    mixins:[JeecgListMixin],
    components: {
      JDictSelectTag,
      JSearchSelectTag,
      CoffeeAttrList,
      CoffeeProductModal
    },
    data () {
      return {
        description: '商品管理管理页面',
        // 表头
        columns: [
          {
            title:'店铺',
            align:"center",
            dataIndex: 'shopId_dictText',
          },
          {
            title:'分类',
            align:"center",
            dataIndex: 'cateId_dictText',
          },
          {
            title:'名称',
            align:"center",
            dataIndex: 'name'
          },
          {
            title:'图片',
            align:"center",
            dataIndex: 'img',
            scopedSlots: {customRender: 'imgSlot'}
          },
          {
            title:'销售量',
            align:"center",
            sorter: true,
            dataIndex: 'sales'
          },
          {
            title:'排序',
            align:"center",
            sorter: true,
            dataIndex: 'sort'
          },
          {
            title:'库存',
            align:"center",
            sorter: true,
            dataIndex: 'stock'
          },
          {
            title:'是否推荐',
            align:"center",
            dataIndex: 'isGood_dictText',
          },
          {
            title:'状态',
            align:"center",
            dataIndex: 'isStatus_dictText',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' },
          }
        ],
        url: {
          list: "/product/coffeeProduct/list",
          delete: "/product/coffeeProduct/delete",
          deleteBatch: "/product/coffeeProduct/deleteBatch",
          exportXlsUrl: "/product/coffeeProduct/exportXls",
          importExcelUrl: "product/coffeeProduct/importExcel",
        },
        dictOptions:{
         isGood:[],
         isStatus:[],
        },
        /* 分页参数 */
        ipagination:{
          current: 1,
          pageSize: 5,
          pageSizeOptions: ['5', '10', '50'],
          showTotal: (total, range) => {
            return range[0] + "-" + range[1] + " 共" + total + "条"
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        selectedMainId:''

      }
    },
    created() {
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      }
    },
    methods: {
      initDictConfig(){
        initDictOptions('coffee_shop,name,id').then((res) => {
          if (res.success) {
            this.$set(this.dictOptions, 'shopId', res.result)
          }
        })
        initDictOptions('coffee_category,name,id').then((res) => {
          if (res.success) {
            this.$set(this.dictOptions, 'cateId', res.result)
          }
        })
        initDictOptions('is_good').then((res) => {
          if (res.success) {
            this.$set(this.dictOptions, 'isGood', res.result)
          }
        })
        initDictOptions('zk_status').then((res) => {
          if (res.success) {
            this.$set(this.dictOptions, 'isStatus', res.result)
          }
        })
      },
      clickThenSelect(record) {
        return {
          on: {
            click: () => {
              this.onSelectChange(record.id.split(","), [record]);
            }
          }
        }
      },
      onClearSelected() {
        this.selectedRowKeys = [];
        this.selectionRows = [];
        this.selectedMainId=''
      },
      onSelectChange(selectedRowKeys, selectionRows) {
        this.selectedMainId=selectedRowKeys[0]
        this.selectedRowKeys = selectedRowKeys;
        this.selectionRows = selectionRows;
      },
      loadData(arg) {
        if(!this.url.list){
          this.$message.error("请设置url.list属性!")
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1;
        }
        this.onClearSelected()
        var params = this.getQueryParams();//查询条件
        this.loading = true;
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            this.dataSource = res.result.records;
            this.ipagination.total = res.result.total;
          }
          if(res.code===510){
            this.$message.warning(res.message)
          }
          this.loading = false;
        })
      }

    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>