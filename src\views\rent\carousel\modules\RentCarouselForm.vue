<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
              <a-input v-model="model.name" placeholder="请输入名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="houseLeaseType">
              <j-dict-select-tag type="radio" v-model="model.houseLeaseType" dictCode="house_lease_type" placeholder="请选择类型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="户型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="roomType">
              <j-dict-select-tag type="radio" v-model="model.roomType" dictCode="house_type" placeholder="请选择户型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="随时入住" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkTime">
              <a-input v-model="model.checkTime" placeholder="入住"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="朝向" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="housePoint">
              <j-dict-select-tag type="radio" v-model="model.housePoint" dictCode="house_point" placeholder="请选择朝向" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="总楼层" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="houseFloor2">
              <a-input v-model="model.houseFloor2" placeholder="请输入总楼层"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="楼层" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="houseFloor1">
              <a-input v-model="model.houseFloor1" placeholder="请输入楼层"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="电梯" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="houseLift">
              <j-dict-select-tag type="radio" v-model="model.houseLift" dictCode="house_lift" placeholder="请选择电梯" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="租期分类" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="category">
              <j-dict-select-tag type="radio" v-model="model.category" dictCode="real_category" placeholder="请选择租期分类" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="面积" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="area">
              <a-input v-model="model.area" placeholder="请输入面积"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="街道" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="city">
              <j-category-select v-model="model.city" pcode="" placeholder="请选择街道"  />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="轮播图" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="image">
              <j-image-upload isMultiple  v-model="model.image" ></j-image-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="联系手机号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="phone">
              <a-input v-model="model.phone" placeholder="请输入联系手机号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="标签" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tag">
              <j-multi-select-tag type="checkbox" v-model="model.tag" dictCode="real_tag" placeholder="请选择标签" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="配置" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="description">
              <j-multi-select-tag type="checkbox" v-model="model.description" dictCode="rent_icon,name,id" placeholder="请选择配置" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="showValue">
            <a-form-model-item label="简介" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="info">
              <j-markdown-editor v-model="model.info" id="info"></j-markdown-editor>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="付款方式" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="payType">
              <j-dict-select-tag type="list" v-model="model.payType" dictCode="rent_pay_type" placeholder="请选择付款方式" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="租金" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="price">
              <a-input v-model="model.price" placeholder="请输入租金"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="押金" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="priceV1">
              <a-input v-model="model.priceV1" placeholder="请输入押金"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="服务费" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="priceV2">
              <a-input v-model="model.priceV2" placeholder="请输入服务费"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="房主联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="adminPhone">
              <a-input v-model="model.adminPhone" placeholder="请输入房主联系电话"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="auth">
            <a-form-model-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status">
              <j-dict-select-tag type="radio" v-model="model.status" dictCode="zk_status" placeholder="请选择状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="auth">
            <a-form-model-item label="房源类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isType">
              <j-dict-select-tag type="radio" v-model="model.isType" dictCode="house_is_type" placeholder="请选择房源类型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="auth">
            <a-form-model-item label="排序" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sort">
              <a-input-number v-model="model.sort" placeholder="请输入排序" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="auth">
            <a-form-model-item label="管理员备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="adminRemark">
              <a-input v-model="model.adminRemark" placeholder="请输入管理员备注"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="详细地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address">
              <a-input-search v-model:value="model.address" placeholder="请输入详细地址"  @search="onSearch"
              enter-button
              ></a-input-search>
            </a-form-model-item>
          </a-col>
          <Maps  ref="mapChild" class="map-sty" :mapKey="mapKey"
            :lat="Number(model.latitude || 39.988383)" :lon="Number(model.longitude || 116.320275)" 
            :address="model.address" 
            @getCoordinates="getCoordinates" 
            />
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'
	import Maps from '@/components/map/map.vue'
  export default {
    name: 'RentCarouselForm',
    components: {
      Maps
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        mapKey:'VIJBZ-UU3EJ-G72FS-KDPKW-WATK6-LPFVL',

        showValue:true,
        auth:true,
        model:{
            houseLift:"有电梯",
            category:"1",
            payType:"季付",
            price:"0",
            priceV1:"0",
            priceV2:"0",
            status:0,
            sort:10,
            description:"1,2,3,4,5,6,7,8,9,10",
            isType:1,
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           name: [
              { required: true, message: '请输入名称!'},
           ],
           houseLeaseType: [
              { required: true, message: '请输入类型!'},
           ],
           roomType: [
              { required: true, message: '请输入户型!'},
           ],
           housePoint: [
              { required: true, message: '请输入朝向!'},
           ],
           houseFloor1: [
              { required: true, message: '请输入楼层!'},
           ],
           houseFloor2: [
              { required: true, message: '请输入总楼层!'},
           ],
           houseLift: [
              { required: true, message: '请输入电梯!'},
           ],
           category: [
              { required: true, message: '请输入租期分类!'},
           ],
           city: [
              { required: true, message: '请选择街道!'},
           ],
           image: [
              { required: true, message: '请选择轮播图!'},
           ],
           phone: [
              { required: true, message: '请输入联系手机号!'},
              { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的联系手机号!'},
           ],
           area: [
              { required: true, message: '请输入面积!'},
              { pattern: /^-?\d+\.?\d*$/, message: '请输入数字!'},
           ],
           price: [
           { required: true,message: '请填写租金!'},
              { pattern: /^-?\d+\.?\d*$/, message: '请输入数字!'},
           ],
           priceV1: [
              { required: false},
              { pattern: /^-?\d+\.?\d*$/, message: '请输入数字!'},
           ],
           priceV2: [
              { required: false},
              { pattern: /^-?\d+\.?\d*$/, message: '请输入数字!'},
           ],
           sort: [
              { required: false},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
           ],
        },
        url: {
          add: "/carousel/rentCarousel/add",
          edit: "/carousel/rentCarousel/edit",
          queryById: "/carousel/rentCarousel/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
      if(this.$store.state.user.info.departIds === "41e57edcdf604eb0a89b9290881adde5") {
          this.auth = false
      }
    },
    methods: {
      onSearch() {
				if(this.$refs.mapChild){
					this.$refs.mapChild.searchKeyword(this.model.address)
				}
      },
      // 地图信息获取
			getCoordinates(data) {
        this.model.latitude = data.location.lat
        this.model.longitude = data.location.lng
        let components = data.addressComponents;
        if(!this.model.address) {
          return false
        }
 /*        if(this.model.address.indexOf(components.street) == -1){
          this.model.address = data.address+(components.town?components.town:'');
        } */
			},
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
        if(this.$store.state.user.info.departIds === "41e57edcdf604eb0a89b9290881adde5" && this.model.status == 0) {
          console.log(this.$store.state.user.info.departIds)
          if(this.model.id){
            this.showValue = false
          }
        }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
      handleCategoryChange(value,backObj){
         this.model = Object.assign(this.model, backObj);
      }
    }
  }
</script>