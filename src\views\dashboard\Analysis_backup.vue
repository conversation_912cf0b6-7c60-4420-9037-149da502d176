<template>
  <div>
    <space-bed v-if="indexStyle==0"></space-bed>
    <div>
    <a-row>
        <a-col :class="getClass(0)" v-on:click="select(0)" :xs="{ span: 5, offset: 1 }" :lg="{ span: 6, offset: 2 }">
          集中式公寓
        </a-col>
        <a-col :class="getClass(1)" v-on:click="select(1)" :xs="{ span: 11, offset: 1 }" :lg="{ span: 6, offset: 1 }">
          分散式公寓
        </a-col>
        <a-col :class="getClass(2)" v-on:click="select(2)" :xs="{ span: 5, offset: 1 }" :lg="{ span: 6, offset: 1 }">
          整租式公寓
        </a-col>
      </a-row>
    </div>
    <!-- <index-chart v-if="indexStyle==0"></index-chart>
    <index-bdc v-if="indexStyle==1"></index-bdc>
    <index-task v-if="indexStyle==2"></index-task> -->
    <a-col v-if="is_show">
      <mapshow :dataSource="dataSource" :key="componentKey" ></mapshow>
    </a-col>
  </div>
</template>

<script>
  var that
  import IndexChart from './IndexChart'
  import IndexTask from "./IndexTask"
  import IndexBdc from './IndexBdc'
  import mapshow from '@/components/zk/mapShow'
  import {httpAction} from '@/api/manage'
  import SpaceTable from '../../components/zk/spaceTable.vue'
  import SpaceBed from '../../components/zk/spaceBed.vue'
  export default {
    name: "Analysis",
    components: {
      IndexChart,
      IndexTask,
      IndexBdc,
      mapshow,
      SpaceTable,
      SpaceBed
    },
    data() {
      return {
        indexStyle:0,
        url: '/flats/common/list',
        dataSource:[],
        is_show:true,
        componentKey: 0
      }
    },
    created() {
      that = this
      //获取站点地址
      that.addressList()
    },
    watch: {
      indexStyle: 'addressList'
    },
    methods: {
       addressList() {
          var url = this.url + '?category=' + this.indexStyle
          httpAction(url, {},'get').then((res) => {
            if (res.success) {
              that.dataSource = res.result
              that.componentKey += 1
            }
          })
       },
       getClass(x) {
         if(this.indexStyle == x) {
            return 'show active'
         } else {
            return 'show'
         }
       },
       select(x) {
          this.indexStyle = x
       } 
    }
  }
</script>
<style scoped>
.show {
  background: rgba(0,160,233,.7);
  font-size: 14px;
  padding: 16px 0;
  text-align: center;
  border-radius: 0;
  min-height: 30px;
  margin-top: 8px;
  margin-bottom: 8px;
  color: #fff;
  cursor:pointer;
}
.active{ 
  background: #00a0e9;
}
</style>